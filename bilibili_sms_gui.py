
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI界面
使用PyQt5创建用户友好的图形界面
"""

import sys
import json
import requests
import urllib.parse
import hashlib
from urllib.parse import urlencode, urlparse, parse_qs
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QLabel, QLineEdit, QPushButton, QTextEdit,
                             QComboBox, QGroupBox, QMessageBox,
                             QTableWidget, QTableWidgetItem, QHeaderView, QFileDialog, QCheckBox)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont, QIcon, QKeySequence
import sqlite3
import os
from datetime import datetime
import pandas as pd

from bilibili_api_utils import BilibiliAPIUtils


class ProxyTestWorker(QThread):
    """代理测试工作线程"""

    # 信号定义
    log_signal = pyqtSignal(str)
    result_signal = pyqtSignal(bool, str)  # 成功/失败, 消息

    def __init__(self, proxy_api_url):
        super().__init__()
        self.proxy_api_url = proxy_api_url

    def run(self):
        """执行代理测试"""
        try:
            self.log_signal.emit("🔍 开始测试代理连接...")

            # 创建带代理的API工具实例进行测试
            proxy_config = {
                'enabled': True,
                'api_url': self.proxy_api_url
            }

            test_api_utils = BilibiliAPIUtils(proxy_config=proxy_config)
            proxy_info = test_api_utils.get_proxy_info()

            if proxy_info:
                self.log_signal.emit(f"✅ 代理测试成功: {proxy_info['ip']}:{proxy_info['port']}")
                self.result_signal.emit(True, f"代理连接成功: {proxy_info['ip']}:{proxy_info['port']}")
            else:
                self.log_signal.emit("❌ 代理获取失败")
                self.result_signal.emit(False, "代理获取失败，请检查API地址")

        except Exception as e:
            error_msg = f"代理测试异常: {str(e)}"
            self.log_signal.emit(f"❌ {error_msg}")
            self.result_signal.emit(False, error_msg)


class ThreadManager:
    """线程管理器"""

    def __init__(self):
        self.active_threads = []

    def add_thread(self, thread):
        """添加线程到管理列表"""
        self.active_threads.append(thread)
        # 连接线程完成信号，自动清理
        thread.finished.connect(lambda: self.remove_thread(thread))

    def remove_thread(self, thread):
        """从管理列表中移除线程"""
        if thread in self.active_threads:
            self.active_threads.remove(thread)

    def cleanup_finished_threads(self):
        """清理已完成的线程"""
        self.active_threads = [t for t in self.active_threads if t.isRunning()]

    def stop_all_threads(self):
        """停止所有活跃线程"""
        for thread in self.active_threads:
            if thread.isRunning():
                thread.terminate()
                thread.wait(3000)  # 等待3秒
        self.active_threads.clear()

    def get_active_count(self):
        """获取活跃线程数量"""
        self.cleanup_finished_threads()
        return len(self.active_threads)


class RegistrationWorker(QThread):
    """注册/登录工作线程"""

    # 信号定义
    log_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int)
    finished_signal = pyqtSignal(dict)

    def __init__(self, sms_worker, verification_code, first_step_headers=None):
        super().__init__()
        self.sms_worker = sms_worker
        self.verification_code = verification_code
        self.first_step_headers = first_step_headers

    def run(self):
        """执行注册或登录"""
        try:
            self.log_signal.emit("🚀 开始在后台线程中执行注册/登录...")
            self.progress_signal.emit(10)

            # 调用SMSWorker的注册方法
            result = self.sms_worker.proceed_with_registration(
                self.verification_code, self.first_step_headers
            )

            self.progress_signal.emit(100)
            self.finished_signal.emit(result)

        except Exception as e:
            self.log_signal.emit(f"💥 注册/登录线程异常: {str(e)}")
            self.finished_signal.emit({
                'success': False,
                'error': f'线程异常: {str(e)}'
            })


class SMSWorker(QThread):
    """短信发送工作线程"""

    # 信号定义
    log_signal = pyqtSignal(str)
    result_signal = pyqtSignal(bool, dict)
    progress_signal = pyqtSignal(int)
    verification_code_signal = pyqtSignal(str)  # 新增：验证码输入信号
    auto_code_received_signal = pyqtSignal(str)  # 新增：自动获取验证码信号

    def __init__(self, phone_number, country_code, captcha_service_url="127.0.0.1:11667/kknsmdsjfkajy3", auto_answer=False, answer_interval=3, auto_rename=False, api_utils=None, sms_receive_url=None, simple_log=False):
        super().__init__()
        self.phone_number = phone_number
        self.country_code = country_code
        # 使用提供的api_utils实例
        if api_utils is not None:
            self.api_utils = api_utils
            # 检查是否有代理配置
            if self.api_utils.is_proxy_enabled():
                proxy_info = self.api_utils.get_proxy_info()
                if proxy_info:
                    self.log_signal.emit(f"🔒 当前会话代理: {proxy_info['ip']}:{proxy_info['port']}")
                    if self.api_utils.proxy_manager and self.api_utils.proxy_manager.is_session_locked():
                        self.log_signal.emit("🔒 会话锁定模式：整个流程将使用此代理")
                else:
                    self.log_signal.emit("🆕 使用新的API工具实例（代理未获取）")
            else:
                self.log_signal.emit("🆕 使用新的API工具实例（无代理）")
        else:
            # 如果没有提供，创建一个无代理的实例作为备用
            self.api_utils = BilibiliAPIUtils()
            self.log_signal.emit("⚠️ 未提供API工具实例，创建新的无代理实例")
        self.captcha_service_url = captcha_service_url
        self.verification_code = None  # 存储用户输入的验证码
        self.step2_result = None  # 存储第二步的结果
        self.auto_answer = auto_answer  # 是否自动答题
        self.answer_interval = answer_interval  # 答题间隔（秒）
        self.auto_rename = auto_rename  # 是否自动随机改名
        self.account_info = None  # 存储账号信息，用于答题
        self.sms_receive_url = sms_receive_url  # 接码地址
        self.auto_sms_enabled = sms_receive_url is not None  # 是否启用自动接码
        self.simple_log = simple_log  # 是否启用简化日志

        # 初始化账号记录管理器
        self.account_manager = AccountRecordManager()

    def emit_log(self, message, is_important=False):
        """
        发送日志信息
        :param message: 日志消息
        :param is_important: 是否为重要信息（简化模式下也会显示）
        """
        if self.simple_log and not is_important:
            # 简化模式下，只显示重要信息
            return
        self.log_signal.emit(message)

    def fetch_sms_code(self, max_attempts=6, wait_seconds=20):
        """
        从接码地址获取验证码
        :param max_attempts: 最大尝试次数
        :param wait_seconds: 每次尝试间隔秒数
        :return: 验证码字符串或None
        """
        if not self.sms_receive_url:
            self.emit_log("❌ 未配置接码地址", is_important=True)
            return None

        import requests
        import re
        import time

        self.emit_log(f"🔍 开始自动获取验证码，最多尝试{max_attempts}次，每次等待{wait_seconds}秒", is_important=True)

        for attempt in range(1, max_attempts + 1):
            try:
                self.emit_log(f"📡 第{attempt}次尝试获取验证码...")

                # 请求接码地址
                response = requests.get(self.sms_receive_url, timeout=10)
                response.raise_for_status()

                content = response.text
                self.emit_log(f"📄 接码响应: {content[:200]}...")

                # 使用正则表达式匹配6位数字验证码
                # 优化正则表达式，支持中文后直接跟数字的情况
                pattern = r'(?<!\d)\d{6}(?!\d)'
                matches = re.findall(pattern, content)

                if matches:
                    # 取最后一个匹配的验证码（通常是最新的）
                    verification_code = matches[-1]
                    self.emit_log(f"✅ 成功获取验证码: {verification_code}", is_important=True)
                    return verification_code
                else:
                    self.emit_log(f"⏳ 第{attempt}次未获取到验证码，等待{wait_seconds}秒后重试...")
                    if attempt < max_attempts:
                        time.sleep(wait_seconds)

            except requests.RequestException as e:
                self.emit_log(f"❌ 第{attempt}次请求失败: {str(e)}")
                if attempt < max_attempts:
                    time.sleep(wait_seconds)
            except Exception as e:
                self.emit_log(f"❌ 第{attempt}次获取验证码异常: {str(e)}")
                if attempt < max_attempts:
                    time.sleep(wait_seconds)

        self.emit_log(f"❌ 尝试{max_attempts}次后仍未获取到验证码", is_important=True)
        return None

    def get_legal_region_from_country_code(self, country_code):
        """根据手机区号获取归属地代码"""
        # 根据手机区号映射到对应的地区代码
        region_map = {
            "86": "CN",    # 中国大陆
            "852": "HK",   # 香港
            "853": "MO",   # 澳门
            "886": "TW",   # 台湾
            "1": "US",     # 美国
            "44": "GB",    # 英国
            "81": "JP",    # 日本
            "82": "KR",    # 韩国
            "65": "SG",    # 新加坡
            "60": "MY",    # 马来西亚
            "66": "TH",    # 泰国
            "84": "VN",    # 越南
            "62": "ID",    # 印度尼西亚
            "63": "PH",    # 菲律宾
            "91": "IN",    # 印度
            "7": "RU",     # 俄罗斯
            "49": "DE",    # 德国
            "33": "FR",    # 法国
            "39": "IT",    # 意大利
            "34": "ES",    # 西班牙
            "31": "NL",    # 荷兰
            "46": "SE",    # 瑞典
            "47": "NO",    # 挪威
            "45": "DK",    # 丹麦
            "358": "FI",   # 芬兰
            "41": "CH",    # 瑞士
            "43": "AT",    # 奥地利
            "32": "BE",    # 比利时
            "351": "PT",   # 葡萄牙
            "30": "GR",    # 希腊
            "48": "PL",    # 波兰
            "420": "CZ",   # 捷克
            "36": "HU",    # 匈牙利
            "40": "RO",    # 罗马尼亚
            "359": "BG",   # 保加利亚
            "385": "HR",   # 克罗地亚
            "386": "SI",   # 斯洛文尼亚
            "421": "SK",   # 斯洛伐克
            "370": "LT",   # 立陶宛
            "371": "LV",   # 拉脱维亚
            "372": "EE",   # 爱沙尼亚
            "61": "AU",    # 澳大利亚
            "64": "NZ",    # 新西兰
            "55": "BR",    # 巴西
            "54": "AR",    # 阿根廷
            "56": "CL",    # 智利
            "57": "CO",    # 哥伦比亚
            "51": "PE",    # 秘鲁
            "58": "VE",    # 委内瑞拉
            "52": "MX",    # 墨西哥
            "27": "ZA",    # 南非
            "20": "EG",    # 埃及
            "234": "NG",   # 尼日利亚
            "254": "KE",   # 肯尼亚
            "212": "MA",   # 摩洛哥
            "213": "DZ",   # 阿尔及利亚
            "216": "TN",   # 突尼斯
            "218": "LY",   # 利比亚
            "220": "GM",   # 冈比亚
            "221": "SN",   # 塞内加尔
            "222": "MR",   # 毛里塔尼亚
            "223": "ML",   # 马里
            "224": "GN",   # 几内亚
            "225": "CI",   # 科特迪瓦
            "226": "BF",   # 布基纳法索
            "227": "NE",   # 尼日尔
            "228": "TG",   # 多哥
            "229": "BJ",   # 贝宁
            "230": "MU",   # 毛里求斯
            "231": "LR",   # 利比里亚
            "232": "SL",   # 塞拉利昂
            "233": "GH",   # 加纳
            "235": "TD",   # 乍得
            "236": "CF",   # 中非共和国
            "237": "CM",   # 喀麦隆
            "238": "CV",   # 佛得角
            "239": "ST",   # 圣多美和普林西比
            "240": "GQ",   # 赤道几内亚
            "241": "GA",   # 加蓬
            "242": "CG",   # 刚果共和国
            "243": "CD",   # 刚果民主共和国
            "244": "AO",   # 安哥拉
            "245": "GW",   # 几内亚比绍
            "246": "IO",   # 英属印度洋领地
            "247": "AC",   # 阿森松岛
            "248": "SC",   # 塞舌尔
            "249": "SD",   # 苏丹
            "250": "RW",   # 卢旺达
            "251": "ET",   # 埃塞俄比亚
            "252": "SO",   # 索马里
            "253": "DJ",   # 吉布提
            "255": "TZ",   # 坦桑尼亚
            "256": "UG",   # 乌干达
            "257": "BI",   # 布隆迪
            "258": "MZ",   # 莫桑比克
            "260": "ZM",   # 赞比亚
            "261": "MG",   # 马达加斯加
            "262": "RE",   # 留尼汪
            "263": "ZW",   # 津巴布韦
            "264": "NA",   # 纳米比亚
            "265": "MW",   # 马拉维
            "266": "LS",   # 莱索托
            "267": "BW",   # 博茨瓦纳
            "268": "SZ",   # 斯威士兰
            "269": "KM",   # 科摩罗
            "290": "SH",   # 圣赫勒拿
            "291": "ER",   # 厄立特里亚
            "297": "AW",   # 阿鲁巴
            "298": "FO",   # 法罗群岛
            "299": "GL",   # 格陵兰
            "350": "GI",   # 直布罗陀
            "352": "LU",   # 卢森堡
            "353": "IE",   # 爱尔兰
            "354": "IS",   # 冰岛
            "355": "AL",   # 阿尔巴尼亚
            "356": "MT",   # 马耳他
            "357": "CY",   # 塞浦路斯
            "373": "MD",   # 摩尔多瓦
            "374": "AM",   # 亚美尼亚
            "375": "BY",   # 白俄罗斯
            "376": "AD",   # 安道尔
            "377": "MC",   # 摩纳哥
            "378": "SM",   # 圣马力诺
            "380": "UA",   # 乌克兰
            "381": "RS",   # 塞尔维亚
            "382": "ME",   # 黑山
            "383": "XK",   # 科索沃
            "385": "HR",   # 克罗地亚
            "387": "BA",   # 波斯尼亚和黑塞哥维那
            "389": "MK",   # 北马其顿
            "423": "LI",   # 列支敦士登
            "500": "FK",   # 福克兰群岛
            "501": "BZ",   # 伯利兹
            "502": "GT",   # 危地马拉
            "503": "SV",   # 萨尔瓦多
            "504": "HN",   # 洪都拉斯
            "505": "NI",   # 尼加拉瓜
            "506": "CR",   # 哥斯达黎加
            "507": "PA",   # 巴拿马
            "508": "PM",   # 圣皮埃尔和密克隆
            "509": "HT",   # 海地
            "590": "GP",   # 瓜德罗普
            "591": "BO",   # 玻利维亚
            "592": "GY",   # 圭亚那
            "593": "EC",   # 厄瓜多尔
            "594": "GF",   # 法属圭亚那
            "595": "PY",   # 巴拉圭
            "596": "MQ",   # 马提尼克
            "597": "SR",   # 苏里南
            "598": "UY",   # 乌拉圭
            "599": "CW",   # 库拉索
            "670": "TL",   # 东帝汶
            "672": "AQ",   # 南极洲
            "673": "BN",   # 文莱
            "674": "NR",   # 瑙鲁
            "675": "PG",   # 巴布亚新几内亚
            "676": "TO",   # 汤加
            "677": "SB",   # 所罗门群岛
            "678": "VU",   # 瓦努阿图
            "679": "FJ",   # 斐济
            "680": "PW",   # 帕劳
            "681": "WF",   # 瓦利斯和富图纳
            "682": "CK",   # 库克群岛
            "683": "NU",   # 纽埃
            "684": "AS",   # 美属萨摩亚
            "685": "WS",   # 萨摩亚
            "686": "KI",   # 基里巴斯
            "687": "NC",   # 新喀里多尼亚
            "688": "TV",   # 图瓦卢
            "689": "PF",   # 法属波利尼西亚
            "690": "TK",   # 托克劳
            "691": "FM",   # 密克罗尼西亚
            "692": "MH",   # 马绍尔群岛
            "850": "KP",   # 朝鲜
            "852": "HK",   # 香港
            "853": "MO",   # 澳门
            "855": "KH",   # 柬埔寨
            "856": "LA",   # 老挝
            "880": "BD",   # 孟加拉国
            "886": "TW",   # 台湾
            "960": "MV",   # 马尔代夫
            "961": "LB",   # 黎巴嫩
            "962": "JO",   # 约旦
            "963": "SY",   # 叙利亚
            "964": "IQ",   # 伊拉克
            "965": "KW",   # 科威特
            "966": "SA",   # 沙特阿拉伯
            "967": "YE",   # 也门
            "968": "OM",   # 阿曼
            "970": "PS",   # 巴勒斯坦
            "971": "AE",   # 阿联酋
            "972": "IL",   # 以色列
            "973": "BH",   # 巴林
            "974": "QA",   # 卡塔尔
            "975": "BT",   # 不丹
            "976": "MN",   # 蒙古
            "977": "NP",   # 尼泊尔
            "992": "TJ",   # 塔吉克斯坦
            "993": "TM",   # 土库曼斯坦
            "994": "AZ",   # 阿塞拜疆
            "995": "GE",   # 格鲁吉亚
            "996": "KG",   # 吉尔吉斯斯坦
            "998": "UZ",   # 乌兹别克斯坦
        }

        return region_map.get(country_code, "CN")  # 默认返回CN

    def solve_geetest_captcha(self, gt, challenge):
        """
        调用打码服务解决极验3验证码
        """
        try:
            self.log_signal.emit(f"🔐 开始处理极验3验证码...")
            self.log_signal.emit(f"  GT: {gt}")
            self.log_signal.emit(f"  Challenge: {challenge}")

            # 构建打码服务URL
            captcha_url = f"http://{self.captcha_service_url}?gt={gt}&challenge={challenge}"
            self.log_signal.emit(f"🌐 打码服务URL: {captcha_url}")

            # 调用打码服务 - 智能选择连接方式
            self.log_signal.emit("📡 正在调用打码服务...")

            # 检查是否是本地服务
            is_local_service = any(local_host in self.captcha_service_url.lower() for local_host in
                                 ['127.0.0.1', 'localhost', '0.0.0.0', '::1'])

            if is_local_service:
                self.log_signal.emit("🏠 检测到本地打码服务，使用直连模式")
                # 本地服务使用直连会话（不通过代理）
                direct_session = self.api_utils.get_direct_session()
                response = direct_session.get(captcha_url, timeout=60)
            else:
                self.log_signal.emit("🌐 检测到远程打码服务，使用代理模式")
                # 远程服务使用代理会话
                tls_session = self.api_utils.get_tls_session()
                if tls_session:
                    response = tls_session.get(captcha_url, timeout=60)
                else:
                    # 备用方案：使用普通requests
                    response = requests.get(captcha_url, timeout=60)

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.log_signal.emit(f"✅ 打码服务响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

                    # 检查打码结果
                    if result.get('success') == 200 or result.get('success') == True or result.get('code') == 0:
                        validate = result.get('validate') or result.get('data', {}).get('validate')
                        seccode = result.get('seccode') or result.get('data', {}).get('seccode')
                        challenge_result = result.get('challenge', challenge)

                        if validate:  # 只要有validate就认为成功，seccode是可选的
                            self.emit_log("🎉 验证码解析成功！", is_important=True)

                            # 构建返回结果
                            captcha_result = {
                                'success': True,
                                'validate': validate,
                                'challenge': challenge_result
                            }

                            # 如果有seccode，也添加进去
                            if seccode:
                                captcha_result['seccode'] = seccode
                            else:
                                # 根据极验3规范，seccode通常是validate + "|jordan"
                                captcha_result['seccode'] = validate + "|jordan"

                            return captcha_result
                        else:
                            self.log_signal.emit("❌ 打码服务返回数据中缺少validate字段")
                            return {'success': False, 'error': '打码服务返回数据中缺少validate字段'}
                    else:
                        error_msg = result.get('message') or result.get('error', '未知错误')
                        self.emit_log(f"❌ 打码服务失败: {error_msg}", is_important=True)
                        return {'success': False, 'error': error_msg}

                except json.JSONDecodeError as e:
                    self.log_signal.emit(f"❌ 打码服务响应JSON解析失败: {str(e)}")
                    self.log_signal.emit(f"📄 原始响应: {response.text}")
                    return {'success': False, 'error': 'JSON解析失败'}
            else:
                self.log_signal.emit(f"❌ 打码服务HTTP错误: {response.status_code}")
                self.log_signal.emit(f"📄 错误内容: {response.text}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}

        except requests.exceptions.Timeout:
            self.log_signal.emit("⏰ 打码服务请求超时")
            return {'success': False, 'error': '打码服务请求超时'}
        except requests.exceptions.ConnectionError:
            self.log_signal.emit("🌐 无法连接到打码服务")
            return {'success': False, 'error': '无法连接到打码服务'}
        except Exception as e:
            self.log_signal.emit(f"💥 打码服务调用异常: {str(e)}")
            return {'success': False, 'error': str(e)}

    def send_sms_step2(self, first_step_response, captcha_data, first_step_headers=None):
        """
        发送第二步SMS验证码
        根据用户提供的API规范实现
        """
        try:
            self.emit_log("\n🚀 开始第二步SMS发送...", is_important=True)
            self.log_signal.emit("📋 使用验证码数据:")
            self.log_signal.emit(f"  gee_challenge: {captcha_data['captcha_challenge']}")
            self.log_signal.emit(f"  gee_validate: {captcha_data['validate']}")
            self.log_signal.emit(f"  gee_seccode: {captcha_data['seccode']}")

            # 从第一步响应中获取recaptcha_token
            # recaptcha_token在recaptcha_url参数中
            recaptcha_url = first_step_response.get('data', {}).get('recaptcha_url', '')
            recaptcha_token = ''
            if recaptcha_url:
                from urllib.parse import urlparse, parse_qs
                parsed_url = urlparse(recaptcha_url)
                query_params = parse_qs(parsed_url.query)
                recaptcha_token = query_params.get('recaptcha_token', [''])[0]

            self.log_signal.emit(f"  recaptcha_url: {recaptcha_url}")
            self.log_signal.emit(f"  recaptcha_token: {recaptcha_token}")

            # 构建第二步请求参数
            params = {
                "appkey": self.api_utils.APPKEY,
                "build": "8530200",
                "buvid": self.api_utils.buvid,
                "c_locale": "zh-Hans_CN",
                "channel": "oppo_tv.danmaku.bili_20200623",
                "cid": self.country_code,
                "device_tourist_id": self.api_utils.device_tourist_id,
                "disable_rcmd": "0",
                "extend": "",
                "gee_challenge": captcha_data['captcha_challenge'],  # 打码服务返回的challenge
                "gee_seccode": captcha_data['seccode'],  # validate + |jordan
                "gee_validate": captcha_data['validate'],
                "local_id": self.api_utils.buvid,
                "login_session_id": self.api_utils.login_session_id,
                "mobi_app": "android",
                "platform": "android",
                "recaptcha_token": recaptcha_token,
                "s_locale": "zh-Hans_CN",
                "spm_id": "main.homepage.bottombar.myinfo",
                "statistics": json.dumps({
                    "appId": 1,
                    "platform": 3,
                    "version": "8.53.0",
                    "abtest": ""
                }, separators=(',', ':')),
                "tel": self.phone_number,
                "ts": self.api_utils.get_current_timestamp()
            }

            # 生成签名
            params["sign"] = self.api_utils.generate_sign(params.copy())

            # 构建请求头 - 使用真实网络数据以提高成功率
            # 先生成用于实际请求的请求头（不包含HTTP/2伪头部）
            headers = self.api_utils.build_headers_for_path(
                path="/x/passport-login/sms/send",
                use_real_network_data=True,
                include_http2_pseudo_headers=False
            )

            # 如果提供了第一步的请求头，使用相同的x-bili-ticket（必须在生成headers之后立即覆盖）
            if first_step_headers and 'x-bili-ticket' in first_step_headers:
                headers['x-bili-ticket'] = first_step_headers['x-bili-ticket']
                self.log_signal.emit("🔄 使用第一步相同的x-bili-ticket")
                self.log_signal.emit(f"🔄 第一步ticket: {first_step_headers['x-bili-ticket'][:50]}...")
                self.log_signal.emit(f"🔄 第二步ticket: {headers['x-bili-ticket'][:50]}...")
            else:
                self.log_signal.emit("⚠️ 未提供第一步请求头，将使用新生成的x-bili-ticket")

            # 生成用于显示的完整请求头（按照真实请求头的顺序）
            # 按照用户提供的真实请求头顺序重新排列
            display_headers = {
                "accept": headers.get("accept", "*/*"),
                "accept-encoding": headers.get("accept-encoding", "gzip, deflate, br"),
                "app-key": headers.get("app-key", "android64"),
                "bili-http-engine": headers.get("bili-http-engine", "ignet"),
                "buvid": headers.get("buvid", ""),
                "content-type": headers.get("content-type", "application/x-www-form-urlencoded; charset=utf-8"),
                "env": headers.get("env", "prod"),
                "fp_local": headers.get("fp_local", ""),
                "fp_remote": headers.get("fp_remote", ""),
                "guestid": headers.get("guestid", ""),
                "session_id": headers.get("session_id", ""),
                "user-agent": headers.get("user-agent", ""),
                "x-bili-locale-bin": headers.get("x-bili-locale-bin", ""),
                "x-bili-metadata-ip-region": headers.get("x-bili-metadata-ip-region", "CN"),
                "x-bili-network-bin": headers.get("x-bili-network-bin", ""),
                "x-bili-ticket": headers.get("x-bili-ticket", ""),
                "x-bili-trace-id": headers.get("x-bili-trace-id", ""),
                ":method": "POST",
                ":authority": "passport.bilibili.com",
                ":path": "/x/passport-login/sms/send",
                ":scheme": "https"
            }

            # 显示第二步请求参数
            self.log_signal.emit("\n📋 第二步请求参数:")
            for key, value in params.items():
                self.log_signal.emit(f"  {key}: {value}")

            # 构建请求体
            data = urlencode(params)
            content_length = str(len(data.encode('utf-8')))
            headers["content-length"] = content_length
            display_headers["content-length"] = content_length

            # 显示第二步请求头（包含HTTP/2伪头部用于调试）
            self.log_signal.emit("\n📋 第二步请求头详情:")
            for key, value in display_headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            # 验证关键请求头字段
            self.log_signal.emit("\n🔍 关键请求头字段验证:")
            expected_headers = {
                "accept": "*/*",
                "accept-encoding": "gzip, deflate, br",
                "app-key": "android64",
                "bili-http-engine": "ignet",
                "content-type": "application/x-www-form-urlencoded; charset=utf-8",
                "env": "prod",
                "x-bili-metadata-ip-region": "CN"
            }

            for key, expected_value in expected_headers.items():
                actual_value = display_headers.get(key, "")
                if actual_value == expected_value:
                    self.log_signal.emit(f"  ✅ {key}: {actual_value}")
                else:
                    self.log_signal.emit(f"  ❌ {key}: 期望 '{expected_value}', 实际 '{actual_value}'")

            # 发送第二步请求
            url = "https://passport.bilibili.com/x/passport-login/sms/send"
            self.log_signal.emit(f"\n🌐 发送第二步SMS请求到: {url}")
            self.log_signal.emit(f"📦 请求体长度: {len(data)} 字节")

            # 使用TLS指纹会话发送请求
            tls_session = self.api_utils.get_tls_session()
            if tls_session:
                response = tls_session.post(
                    url=url,
                    data=data,
                    headers=headers,
                    timeout=30
                )
            else:
                # 备用方案：使用普通requests
                response = requests.post(
                    url=url,
                    data=data,
                    headers=headers,
                    timeout=30
                )

            # 解析第二步响应
            self.log_signal.emit(f"📨 第二步响应状态码: {response.status_code}")

            # 显示响应头
            self.log_signal.emit("📋 第二步响应头:")
            for key, value in response.headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.log_signal.emit("✅ 第二步响应解析成功")
                    self.log_signal.emit(f"📄 第二步响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                    # 检查第二步业务状态码
                    if result.get("code") == 0:
                        self.emit_log("🎉 第二步SMS发送成功！", is_important=True)
                        data = result.get("data", {})
                        if "is_new" in data:
                            user_type = "新用户" if data["is_new"] else "老用户"
                            self.log_signal.emit(f"👤 用户类型: {user_type}")
                        if "captcha_key" in data:
                            self.log_signal.emit(f"📱 验证码密钥: {data['captcha_key']}")
                        return {'success': True, 'data': result}
                    else:
                        error_msg = result.get('message', '未知错误')
                        self.log_signal.emit(f"❌ 第二步业务错误: {error_msg}")
                        return {'success': False, 'error': error_msg, 'data': result}

                except json.JSONDecodeError as e:
                    self.log_signal.emit(f"❌ 第二步响应JSON解析失败: {str(e)}")
                    self.log_signal.emit(f"📄 原始响应: {response.text}")
                    return {'success': False, 'error': 'JSON解析失败'}
            else:
                self.log_signal.emit(f"❌ 第二步HTTP请求失败: {response.status_code}")
                self.log_signal.emit(f"📄 错误内容: {response.text}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}

        except requests.exceptions.Timeout:
            self.log_signal.emit("⏰ 第二步请求超时")
            return {'success': False, 'error': '第二步请求超时'}
        except requests.exceptions.ConnectionError:
            self.log_signal.emit("🌐 第二步网络连接错误")
            return {'success': False, 'error': '第二步网络连接错误'}
        except Exception as e:
            self.log_signal.emit(f"💥 第二步未知错误: {str(e)}")
            return {'success': False, 'error': str(e)}

    def register_step3(self, step2_result, verification_code, first_step_headers=None):
        """
        第三步：注册用户
        根据用户提供的API规范实现注册接口
        """
        try:
            self.emit_log("\n🚀 开始第三步用户注册...", is_important=True)
            self.log_signal.emit("📋 使用注册数据:")
            self.log_signal.emit(f"  手机号: {self.phone_number}")
            self.log_signal.emit(f"  验证码: {verification_code}")

            # 从第二步结果中获取captcha_key
            step2_data = step2_result.get('data', {}).get('data', {})
            captcha_key = step2_data.get('captcha_key', '')

            if not captcha_key:
                self.log_signal.emit("❌ 缺少captcha_key，无法进行注册")
                return {'success': False, 'error': '缺少captcha_key'}

            self.log_signal.emit(f"  captcha_key: {captcha_key}")

            # 构建第三步注册请求参数
            params = {
                "appkey": self.api_utils.APPKEY,
                "bili_local_id": self.api_utils.fp_local,  # 这个就是第一步fp_local的值
                "build": "8530200",  # 版本号固定
                "buvid": self.api_utils.buvid,  # 要和第一步的一样(因为全程都要是同一个设备进行注册的)
                "c_locale": "zh-Hans_CN",  # 固定
                "captcha_key": captcha_key,  # 第二步响应的captcha_key的值
                "channel": "oppo_tv.danmaku.bili_20200623",  # 固定
                "cid": self.country_code,  # 这是输入的手机号和第一步的一样
                "code": verification_code,  # 这里是验证码
                "device": "phone",  # 固定
                "device_id": self.api_utils.fp_local,  # 这个就是第一步fp_local的值
                "device_meta": self.generate_device_meta(),  # device_meta得值要根据官方得算法得来的，很长。跟手机设备信息有关系
                "device_name": self.generate_device_name(),  # 这个要根据官方得算法得来，跟手机设备有关系
                "device_platform": self.generate_device_platform(),  # 这个要根据官方得算法得来，跟手机设备有关系
                "device_tourist_id": self.api_utils.device_tourist_id,  # 要和第一步的一样(因为全程都要是同一个设备进行注册的)
                "disable_rcmd": "0",  # 固定
                "dt": self.generate_dt(),  # 这个要根据官方得算法得来，跟手机设备有关系
                "extend": "",  # 固定
                "from_pv": "",  # 固定
                "from_url": "",  # 固定
                "local_id": self.api_utils.buvid,  # 要和第一步的一样(因为全程都要是同一个设备进行注册的)
                "login_session_id": self.api_utils.login_session_id,  # 要和第一步的一样(因为全程都要是同一个设备进行注册的)
                "mobi_app": "android",  # 固定
                "platform": "android",  # 固定
                "s_locale": "zh-Hans_CN",  # 固定
                "scene": "",  # 固定
                "spm_id": "main.homepage.bottombar.myinfo",  # 固定
                "statistics": json.dumps({
                    "appId": 1,
                    "platform": 3,
                    "version": "8.53.0",
                    "abtest": ""
                }, separators=(',', ':')),  # 固定
                "tel": self.phone_number,  # 这是输入的手机号和第一步的一样
                "ts": self.api_utils.get_current_timestamp()  # 根据官方文档得api文档里面的算法得来(查阅官方文档当前时间戳)
            }

            # 生成签名
            params["sign"] = self.api_utils.generate_sign(params.copy())

            # 构建请求头 - 使用真实网络数据以提高成功率
            # 先生成用于实际请求的请求头（不包含HTTP/2伪头部）
            headers = self.api_utils.build_headers_for_path(
                path="/x/passport-user/reg/sms",
                use_real_network_data=True,
                include_http2_pseudo_headers=False
            )

            # 如果提供了第一步的请求头，使用相同的x-bili-ticket（必须在生成headers之后立即覆盖）
            if first_step_headers and 'x-bili-ticket' in first_step_headers:
                headers['x-bili-ticket'] = first_step_headers['x-bili-ticket']
                self.log_signal.emit("🔄 使用第一步相同的x-bili-ticket")
                self.log_signal.emit(f"🔄 第一步ticket: {first_step_headers['x-bili-ticket'][:50]}...")
                self.log_signal.emit(f"🔄 第三步ticket: {headers['x-bili-ticket'][:50]}...")
            else:
                self.log_signal.emit("⚠️ 未提供第一步请求头，将使用新生成的x-bili-ticket")

            # 生成用于显示的完整请求头（按照真实请求头的顺序）
            # 按照用户提供的真实请求头顺序重新排列
            display_headers = {
                "accept": headers.get("accept", "*/*"),
                "accept-encoding": headers.get("accept-encoding", "gzip, deflate, br"),
                "app-key": headers.get("app-key", "android64"),
                "bili-http-engine": headers.get("bili-http-engine", "ignet"),
                "buvid": headers.get("buvid", ""),
                "content-type": headers.get("content-type", "application/x-www-form-urlencoded; charset=utf-8"),
                "env": headers.get("env", "prod"),
                "fp_local": headers.get("fp_local", ""),
                "fp_remote": headers.get("fp_remote", ""),
                "guestid": headers.get("guestid", ""),
                "session_id": headers.get("session_id", ""),
                "user-agent": headers.get("user-agent", ""),
                "x-bili-locale-bin": headers.get("x-bili-locale-bin", ""),
                "x-bili-metadata-ip-region": headers.get("x-bili-metadata-ip-region", "CN"),
                "x-bili-network-bin": headers.get("x-bili-network-bin", ""),
                "x-bili-ticket": headers.get("x-bili-ticket", ""),
                "x-bili-trace-id": headers.get("x-bili-trace-id", ""),
                ":method": "POST",
                ":authority": "passport.bilibili.com",
                ":path": "/x/passport-user/reg/sms",
                ":scheme": "https"
            }

            # 显示第三步请求参数
            self.log_signal.emit("\n📋 第三步请求参数:")
            for key, value in params.items():
                # 对于长字段进行适当截断显示
                if key in ['device_meta', 'dt'] and len(str(value)) > 100:
                    display_value = str(value)[:100] + "..."
                else:
                    display_value = str(value)
                self.log_signal.emit(f"  {key}: {display_value}")

            # 构建请求体
            data = urlencode(params)
            content_length = str(len(data.encode('utf-8')))
            headers["content-length"] = content_length
            display_headers["content-length"] = content_length

            # 显示第三步请求头（包含HTTP/2伪头部用于调试）
            self.log_signal.emit("\n📋 第三步请求头详情:")
            for key, value in display_headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            # 验证关键请求头字段
            self.log_signal.emit("\n🔍 关键请求头字段验证:")
            expected_headers = {
                "accept": "*/*",
                "accept-encoding": "gzip, deflate, br",
                "app-key": "android64",
                "bili-http-engine": "ignet",
                "content-type": "application/x-www-form-urlencoded; charset=utf-8",
                "env": "prod",
                "x-bili-metadata-ip-region": "CN"
            }

            for key, expected_value in expected_headers.items():
                actual_value = display_headers.get(key, "")
                if actual_value == expected_value:
                    self.log_signal.emit(f"  ✅ {key}: {actual_value}")
                else:
                    self.log_signal.emit(f"  ❌ {key}: 期望 '{expected_value}', 实际 '{actual_value}'")

            # 发送第三步请求
            url = "https://passport.bilibili.com/x/passport-user/reg/sms"
            self.log_signal.emit(f"\n🌐 发送第三步注册请求到: {url}")
            self.log_signal.emit(f"📦 请求体长度: {len(data)} 字节")

            # 使用TLS指纹会话发送请求
            tls_session = self.api_utils.get_tls_session()
            if tls_session:
                response = tls_session.post(
                    url=url,
                    data=data,
                    headers=headers,
                    timeout=30
                )
            else:
                # 备用方案：使用普通requests
                response = requests.post(
                    url=url,
                    data=data,
                    headers=headers,
                    timeout=30
                )

            # 解析第三步响应
            self.log_signal.emit(f"📨 第三步响应状态码: {response.status_code}")

            # 显示响应头
            self.log_signal.emit("📋 第三步响应头:")
            for key, value in response.headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.log_signal.emit("✅ 第三步响应解析成功")
                    self.log_signal.emit(f"📄 第三步响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                    # 检查第三步业务状态码
                    if result.get("code") == 0:
                        data = result.get("data", {})

                        # 检查是否为高危账号
                        if data.get('status') == 2 and '高危异常行为' in str(data.get('message', '')):
                            self.log_signal.emit("🚨 检测到高危账号！")
                            high_risk_message = data.get('message', '账号存在高危异常行为')
                            high_risk_url = data.get('url', '')
                            self.log_signal.emit(f"⚠️ 高危信息: {high_risk_message}")
                            if high_risk_url:
                                self.log_signal.emit(f"🔗 处理链接: {high_risk_url}")
                            self.log_signal.emit("🛑 会话已停止，请处理高危问题后重试")
                            return {'success': False, 'error': '检测到高危账号', 'step4_data': result, 'high_risk': True}

                        self.emit_log("🎉 第三步用户注册成功！", is_important=True)
                        if "hint" in data:
                            self.log_signal.emit(f"提示信息: {data['hint']}")
                        return {'success': True, 'data': result}
                    else:
                        error_msg = result.get('message', '未知错误')
                        self.log_signal.emit(f"❌ 第三步业务错误: {error_msg}")
                        return {'success': False, 'error': error_msg, 'data': result}

                except json.JSONDecodeError as e:
                    self.log_signal.emit(f"❌ 第三步响应JSON解析失败: {str(e)}")
                    self.log_signal.emit(f"📄 原始响应: {response.text}")
                    return {'success': False, 'error': 'JSON解析失败'}
            else:
                self.log_signal.emit(f"❌ 第三步HTTP请求失败: {response.status_code}")
                self.log_signal.emit(f"📄 错误内容: {response.text}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}

        except requests.exceptions.Timeout:
            self.log_signal.emit("⏰ 第三步请求超时")
            return {'success': False, 'error': '第三步请求超时'}
        except requests.exceptions.ConnectionError:
            self.log_signal.emit("🌐 第三步网络连接错误")
            return {'success': False, 'error': '第三步网络连接错误'}
        except Exception as e:
            self.log_signal.emit(f"💥 第三步未知错误: {str(e)}")
            return {'success': False, 'error': str(e)}

    def generate_device_meta(self):
        """
        生成device_meta参数
        根据官方算法生成，跟手机设备信息有关系
        """
        import base64
        import json

        # 获取构建信息
        build_info = self.api_utils.device_info.get('build_info', {})

        # 构建设备元数据
        device_meta = {
            "imei": self.api_utils.device_info.get('imei', ''),
            "android_id": self.api_utils.device_info.get('android_id', ''),
            "mac": self.api_utils.device_info.get('mac_address', ''),
            "brand": self.api_utils.device_info.get('brand', ''),
            "model": self.api_utils.device_info.get('model', ''),
            "build_id": self.api_utils.device_info.get('build_id', build_info.get('build_id', '')),
            "fingerprint": build_info.get('fingerprint', self.api_utils.device_info.get('device_fingerprint', '')),
            "serial": self.api_utils.device_info.get('serial_number', ''),
            "cpu_abi": self.api_utils.device_info.get('cpu_abi', ''),
            "screen": self.api_utils.device_info.get('screen_resolution', ''),
            "memory": str(self.api_utils.device_info.get('memory_total', 0)),
            "storage": str(self.api_utils.device_info.get('storage_total', 0)),
            # 添加Android版本信息
            "android_version": self.api_utils.device_info.get('android_version', ''),
            # 添加构建时间
            "build_time": str(build_info.get('build_time', 0)),
            # 添加增量版本
            "incremental": build_info.get('incremental', '')
        }

        # 转换为JSON字符串并Base64编码
        meta_json = json.dumps(device_meta, separators=(',', ':'))
        meta_base64 = base64.b64encode(meta_json.encode('utf-8')).decode('ascii')

        return meta_base64

    def generate_device_name(self):
        """
        生成device_name参数
        格式: {Brand}{Model}
        """
        brand = self.api_utils.device_info.get('brand', 'OnePlus')
        model = self.api_utils.device_info.get('model', 'PJD110')
        return f"{brand}{model}"

    def generate_device_platform(self):
        """
        生成device_platform参数
        格式: Android{Version}{Brand}{Model}
        """
        android_version = self.api_utils.device_info.get('android_version', '15')
        brand = self.api_utils.device_info.get('brand', 'OnePlus')
        model = self.api_utils.device_info.get('model', 'PJD110')
        return f"Android{android_version}{brand}{model}"

    def generate_dt(self):
        """
        生成dt参数
        根据官方算法生成，跟手机设备有关系
        这是一个加密的设备特征字符串
        """
        import base64
        import hashlib
        import time

        # 构建设备特征数据
        device_features = [
            self.api_utils.device_info.get('imei', ''),
            self.api_utils.device_info.get('android_id', ''),
            self.api_utils.device_info.get('mac_address', ''),
            self.api_utils.device_info.get('serial_number', ''),
            self.api_utils.device_info.get('device_fingerprint', ''),
            str(int(time.time())),  # 时间戳
            self.api_utils.APPKEY,  # 应用密钥
        ]

        # 生成特征哈希
        feature_string = '|'.join(device_features)
        feature_hash = hashlib.sha256(feature_string.encode('utf-8')).digest()

        # Base64编码并添加换行符（模拟官方格式）
        dt_base64 = base64.b64encode(feature_hash).decode('ascii')

        # 按照官方格式添加换行符
        formatted_dt = ""
        for i in range(0, len(dt_base64), 64):
            formatted_dt += dt_base64[i:i+64] + "\n"

        return formatted_dt

    def exchange_account_info(self, registration_code, first_step_headers=None):
        """
        第四步：使用注册码换取账号信息
        根据用户提供的API规范实现access_token接口
        """
        try:
            self.emit_log("\n🚀 开始第四步换取账号信息...", is_important=True)
            self.log_signal.emit("📋 使用注册码:")
            self.log_signal.emit(f"  注册码: {registration_code}")

            # 构建第四步请求参数
            params = {
                "appkey": self.api_utils.APPKEY,  # 固定
                "bili_local_id": self.api_utils.fp_local,  # 这个就是第一步fp_local的值
                "build": "8530200",  # 固定
                "buvid": self.api_utils.buvid,  # 要和第一步的一样(因为全程都要是同一个设备进行注册的)
                "c_locale": "zh-Hans_CN",  # 固定
                "channel": "oppo_tv.danmaku.bili_20200623",  # 固定
                "code": registration_code,  # 这个地方填第三步响应成功后的code得值
                "device": "phone",  # 固定
                "device_id": self.api_utils.fp_local,  # 这个就是第一步fp_local的值
                "device_name": self.generate_device_name(),  # 和第三步的一样，这个要根据官方得算法得来，跟手机设备有关系
                "device_platform": self.generate_device_platform(),  # 和第三步的一样，这个要根据官方得算法得来，跟手机设备有关系
                "disable_rcmd": "0",  # 固定
                "grant_type": "authorization_code",  # 固定
                "local_id": self.api_utils.buvid,  # 要和第一步的一样(因为全程都要是同一个设备进行注册的)
                "mobi_app": "android",  # 固定
                "platform": "android",  # 固定
                "s_locale": "zh-Hans_CN",  # 固定
                "statistics": json.dumps({
                    "appId": 1,
                    "platform": 3,
                    "version": "8.53.0",
                    "abtest": ""
                }, separators=(',', ':')),  # 固定
                "ts": self.api_utils.get_current_timestamp()  # 根据官方文档得api文档里面的算法得来(查阅官方文档当前时间戳)
            }

            # 生成签名
            params["sign"] = self.api_utils.generate_sign(params.copy())

            # 构建请求头 - 使用真实网络数据以提高成功率
            # 先生成用于实际请求的请求头（不包含HTTP/2伪头部）
            headers = self.api_utils.build_headers_for_path(
                path="/x/passport-login/oauth2/access_token",
                use_real_network_data=True,
                include_http2_pseudo_headers=False
            )

            # 如果提供了第一步的请求头，使用相同的x-bili-ticket（必须在生成headers之后立即覆盖）
            if first_step_headers and 'x-bili-ticket' in first_step_headers:
                headers['x-bili-ticket'] = first_step_headers['x-bili-ticket']
                self.log_signal.emit("🔄 使用第一步相同的x-bili-ticket")
                self.log_signal.emit(f"🔄 第一步ticket: {first_step_headers['x-bili-ticket'][:50]}...")
                self.log_signal.emit(f"🔄 第四步ticket: {headers['x-bili-ticket'][:50]}...")
            else:
                self.log_signal.emit("⚠️ 未提供第一步请求头，将使用新生成的x-bili-ticket")

            # 生成用于显示的完整请求头（按照真实请求头的顺序）
            # 按照用户提供的真实请求头顺序重新排列
            display_headers = {
                "accept": headers.get("accept", "*/*"),
                "accept-encoding": headers.get("accept-encoding", "gzip, deflate, br"),
                "app-key": headers.get("app-key", "android64"),
                "bili-http-engine": headers.get("bili-http-engine", "ignet"),
                "buvid": headers.get("buvid", ""),
                "content-type": headers.get("content-type", "application/x-www-form-urlencoded; charset=utf-8"),
                "env": headers.get("env", "prod"),
                "fp_local": headers.get("fp_local", ""),
                "fp_remote": headers.get("fp_remote", ""),
                "guestid": headers.get("guestid", ""),
                "session_id": headers.get("session_id", ""),
                "user-agent": headers.get("user-agent", ""),
                "x-bili-locale-bin": headers.get("x-bili-locale-bin", ""),
                "x-bili-metadata-ip-region": headers.get("x-bili-metadata-ip-region", "CN"),
                "x-bili-network-bin": headers.get("x-bili-network-bin", ""),
                "x-bili-ticket": headers.get("x-bili-ticket", ""),
                "x-bili-trace-id": headers.get("x-bili-trace-id", ""),
                ":method": "POST",
                ":authority": "passport.bilibili.com",
                ":path": "/x/passport-login/oauth2/access_token",
                ":scheme": "https"
            }

            # 显示第四步请求参数
            self.log_signal.emit("\n📋 第四步请求参数:")
            for key, value in params.items():
                # 对于长字段进行适当截断显示
                if key in ['device_name', 'device_platform'] and len(str(value)) > 100:
                    display_value = str(value)[:100] + "..."
                else:
                    display_value = str(value)
                self.log_signal.emit(f"  {key}: {display_value}")

            # 构建请求体
            data = urlencode(params)
            content_length = str(len(data.encode('utf-8')))
            headers["content-length"] = content_length
            display_headers["content-length"] = content_length

            # 显示第四步请求头（包含HTTP/2伪头部用于调试）
            self.log_signal.emit("\n📋 第四步请求头详情:")
            for key, value in display_headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            # 验证关键请求头字段
            self.log_signal.emit("\n🔍 关键请求头字段验证:")
            expected_headers = {
                "accept": "*/*",
                "accept-encoding": "gzip, deflate, br",
                "app-key": "android64",
                "bili-http-engine": "ignet",
                "content-type": "application/x-www-form-urlencoded; charset=utf-8",
                "env": "prod",
                "x-bili-metadata-ip-region": "CN"
            }

            for key, expected_value in expected_headers.items():
                actual_value = display_headers.get(key, "")
                if actual_value == expected_value:
                    self.log_signal.emit(f"  ✅ {key}: {actual_value}")
                else:
                    self.log_signal.emit(f"  ❌ {key}: 期望 '{expected_value}', 实际 '{actual_value}'")

            # 发送第四步请求
            url = "https://passport.bilibili.com/x/passport-login/oauth2/access_token"
            self.log_signal.emit(f"\n🌐 发送第四步换取账号信息请求到: {url}")
            self.log_signal.emit(f"📦 请求体长度: {len(data)} 字节")

            # 使用TLS指纹会话发送请求
            tls_session = self.api_utils.get_tls_session()
            if tls_session:
                response = tls_session.post(
                    url=url,
                    data=data,
                    headers=headers,
                    timeout=30
                )
            else:
                # 备用方案：使用普通requests
                response = requests.post(
                    url=url,
                    data=data,
                    headers=headers,
                    timeout=30
                )

            # 解析第四步响应
            self.log_signal.emit(f"📨 第四步响应状态码: {response.status_code}")

            # 显示响应头
            self.log_signal.emit("📋 第四步响应头:")
            for key, value in response.headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.log_signal.emit("✅ 第四步响应解析成功")
                    self.log_signal.emit(f"📄 第四步响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                    # 检查第四步业务状态码
                    if result.get("code") == 0:
                        self.emit_log("🎉 第四步换取账号信息成功！", is_important=True)
                        data = result.get("data", {})

                        # 显示完整的关键信息
                        token_info = data.get("token_info", {})
                        if token_info:
                            if "access_token" in token_info:
                                self.log_signal.emit(f"access_token：{token_info['access_token']}")

                            if "refresh_token" in token_info:
                                self.log_signal.emit(f"\nrefresh_token：{token_info['refresh_token']}")

                        # 显示完整的Cookie信息
                        cookie_info = data.get("cookie_info", {})
                        if cookie_info:
                            cookies = cookie_info.get("cookies", [])
                            # 将所有cookies连在一起，用分号分隔
                            cookie_parts = []
                            for cookie in cookies:
                                cookie_name = cookie.get("name", "")
                                cookie_value = cookie.get("value", "")
                                if cookie_name and cookie_value:
                                    cookie_parts.append(f"{cookie_name}={cookie_value}")
                            if cookie_parts:
                                self.log_signal.emit(f"\ncookies：{'; '.join(cookie_parts)}")

                        # 显示提示信息
                        self.log_signal.emit(f"\n💬 提示信息: 注册成功")

                        return {'success': True, 'data': result}
                    else:
                        error_msg = result.get('message', '未知错误')
                        self.log_signal.emit(f"❌ 第四步业务错误: {error_msg}")
                        return {'success': False, 'error': error_msg, 'data': result}

                except json.JSONDecodeError as e:
                    self.log_signal.emit(f"❌ 第四步响应JSON解析失败: {str(e)}")
                    self.log_signal.emit(f"📄 原始响应: {response.text}")
                    return {'success': False, 'error': 'JSON解析失败'}
            else:
                self.log_signal.emit(f"❌ 第四步HTTP请求失败: {response.status_code}")
                self.log_signal.emit(f"📄 错误内容: {response.text}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}

        except requests.exceptions.Timeout:
            self.log_signal.emit("⏰ 第四步请求超时")
            return {'success': False, 'error': '第四步请求超时'}
        except requests.exceptions.ConnectionError:
            self.log_signal.emit("🌐 第四步网络连接错误")
            return {'success': False, 'error': '第四步网络连接错误'}
        except Exception as e:
            self.log_signal.emit(f"💥 第四步未知错误: {str(e)}")
            return {'success': False, 'error': str(e)}

    def login_step5(self, step2_result, verification_code, first_step_headers=None):
        """
        第五步：老用户登录
        根据用户提供的登录接口规范实现
        """
        try:
            self.emit_log("\n🚀 开始第五步老用户登录...", is_important=True)
            self.log_signal.emit("📋 使用登录数据:")
            self.log_signal.emit(f"  手机号: {self.phone_number}")
            self.log_signal.emit(f"  验证码: {verification_code}")

            # 从第二步结果中获取captcha_key
            step2_data = step2_result.get('data', {}).get('data', {})
            captcha_key = step2_data.get('captcha_key', '')

            if not captcha_key:
                self.log_signal.emit("❌ 缺少captcha_key，无法进行登录")
                return {'success': False, 'error': '缺少captcha_key'}

            self.log_signal.emit(f"  captcha_key: {captcha_key}")

            # 构建第五步登录请求参数
            params = {
                "appkey": self.api_utils.APPKEY,
                "bili_local_id": self.api_utils.fp_local,  # 这个就是第一步fp_local的值
                "build": "8530200",  # 版本号固定
                "buvid": self.api_utils.buvid,  # 要和第一步的一样(因为全程都要是同一个设备进行的)
                "c_locale": "zh-Hans_CN",  # 固定
                "captcha_key": captcha_key,  # 第二步响应的captcha_key的值
                "channel": "oppo_tv.danmaku.bili_20200623",  # 固定
                "cid": self.country_code,  # 这是输入的手机号和第一步的一样
                "code": verification_code,  # 这里是验证码
                "device": "phone",  # 固定
                "device_id": self.api_utils.fp_local,  # 这个就是第一步fp_local的值
                "device_meta": self.generate_device_meta(),  # device_meta得值要根据官方得算法得来的，很长。跟手机设备信息有关系
                "device_name": self.generate_device_name(),  # 这个要根据官方得算法得来，跟手机设备有关系
                "device_platform": self.generate_device_platform(),  # 这个要根据官方得算法得来，跟手机设备有关系
                "device_tourist_id": self.api_utils.device_tourist_id,  # 要和第一步的一样(因为全程都要是同一个设备进行的)
                "disable_rcmd": "0",  # 固定
                "dt": self.generate_dt(),  # 这个要根据官方得算法得来，跟手机设备有关系
                "extend": "",  # 固定
                "from_pv": "",  # 固定
                "from_url": "",  # 固定
                "local_id": self.api_utils.buvid,  # 要和第一步的一样(因为全程都要是同一个设备进行的)
                "login_session_id": self.api_utils.login_session_id,  # 要和第一步的一样(因为全程都要是同一个设备进行的)
                "mobi_app": "android",  # 固定
                "platform": "android",  # 固定
                "s_locale": "zh-Hans_CN",  # 固定
                "scene": "",  # 固定
                "spm_id": "main.homepage.bottombar.myinfo",  # 固定
                "statistics": json.dumps({
                    "appId": 1,
                    "platform": 3,
                    "version": "8.53.0",
                    "abtest": ""
                }, separators=(',', ':')),  # 固定
                "tel": self.phone_number,  # 这是输入的手机号和第一步的一样
                "ts": self.api_utils.get_current_timestamp()  # 根据官方文档得api文档里面的算法得来(查阅官方文档当前时间戳)
            }

            # 生成签名
            params["sign"] = self.api_utils.generate_sign(params.copy())

            # 构建请求头 - 使用真实网络数据以提高成功率
            # 先生成用于实际请求的请求头（不包含HTTP/2伪头部）
            headers = self.api_utils.build_headers_for_path(
                path="/x/passport-login/login/sms",
                use_real_network_data=True,
                include_http2_pseudo_headers=False
            )

            # 如果提供了第一步的请求头，使用相同的x-bili-ticket（必须在生成headers之后立即覆盖）
            if first_step_headers and 'x-bili-ticket' in first_step_headers:
                headers['x-bili-ticket'] = first_step_headers['x-bili-ticket']
                self.log_signal.emit("🔄 使用第一步相同的x-bili-ticket")
                self.log_signal.emit(f"🔄 第一步ticket: {first_step_headers['x-bili-ticket'][:50]}...")
                self.log_signal.emit(f"🔄 第五步ticket: {headers['x-bili-ticket'][:50]}...")
            else:
                self.log_signal.emit("⚠️ 未提供第一步请求头，将使用新生成的x-bili-ticket")

            # 生成用于显示的完整请求头（按照真实请求头的顺序）
            # 按照用户提供的真实请求头顺序重新排列
            display_headers = {
                "accept": headers.get("accept", "*/*"),
                "accept-encoding": headers.get("accept-encoding", "gzip, deflate, br"),
                "app-key": headers.get("app-key", "android64"),
                "bili-http-engine": headers.get("bili-http-engine", "ignet"),
                "buvid": headers.get("buvid", ""),
                "content-type": headers.get("content-type", "application/x-www-form-urlencoded; charset=utf-8"),
                "env": headers.get("env", "prod"),
                "fp_local": headers.get("fp_local", ""),
                "fp_remote": headers.get("fp_remote", ""),
                "guestid": headers.get("guestid", ""),
                "session_id": headers.get("session_id", ""),
                "user-agent": headers.get("user-agent", ""),
                "x-bili-locale-bin": headers.get("x-bili-locale-bin", ""),
                "x-bili-metadata-ip-region": headers.get("x-bili-metadata-ip-region", "CN"),
                "x-bili-network-bin": headers.get("x-bili-network-bin", ""),
                "x-bili-ticket": headers.get("x-bili-ticket", ""),
                "x-bili-trace-id": headers.get("x-bili-trace-id", ""),
                ":method": "POST",
                ":authority": "passport.bilibili.com",
                ":path": "/x/passport-login/login/sms",
                ":scheme": "https"
            }

            # 显示第五步请求参数
            self.log_signal.emit("\n📋 第五步请求参数:")
            for key, value in params.items():
                # 对于长字段进行适当截断显示
                if key in ['device_meta', 'dt'] and len(str(value)) > 100:
                    display_value = str(value)[:100] + "..."
                else:
                    display_value = str(value)
                self.log_signal.emit(f"  {key}: {display_value}")

            # 构建请求体
            data = urlencode(params)
            content_length = str(len(data.encode('utf-8')))
            headers["content-length"] = content_length
            display_headers["content-length"] = content_length

            # 显示第五步请求头（包含HTTP/2伪头部用于调试）
            self.log_signal.emit("\n📋 第五步请求头详情:")
            for key, value in display_headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            # 验证关键请求头字段
            self.log_signal.emit("\n🔍 关键请求头字段验证:")
            expected_headers = {
                "accept": "*/*",
                "accept-encoding": "gzip, deflate, br",
                "app-key": "android64",
                "bili-http-engine": "ignet",
                "content-type": "application/x-www-form-urlencoded; charset=utf-8",
                "env": "prod",
                "x-bili-metadata-ip-region": "CN"
            }

            for key, expected_value in expected_headers.items():
                actual_value = display_headers.get(key, "")
                if actual_value == expected_value:
                    self.log_signal.emit(f"  ✅ {key}: {actual_value}")
                else:
                    self.log_signal.emit(f"  ❌ {key}: 期望 '{expected_value}', 实际 '{actual_value}'")

            # 发送第五步请求
            url = "https://passport.bilibili.com/x/passport-login/login/sms"
            self.log_signal.emit(f"\n🌐 发送第五步登录请求到: {url}")
            self.log_signal.emit(f"📦 请求体长度: {len(data)} 字节")

            # 使用TLS指纹会话发送请求
            tls_session = self.api_utils.get_tls_session()
            if tls_session:
                response = tls_session.post(
                    url=url,
                    data=data,
                    headers=headers,
                    timeout=30
                )
            else:
                # 备用方案：使用普通requests
                response = requests.post(
                    url=url,
                    data=data,
                    headers=headers,
                    timeout=30
                )

            # 解析第五步响应
            self.log_signal.emit(f"📨 第五步响应状态码: {response.status_code}")

            # 显示响应头
            self.log_signal.emit("📋 第五步响应头:")
            for key, value in response.headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.log_signal.emit("✅ 第五步响应解析成功")
                    self.log_signal.emit(f"📄 第五步响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                    # 检查第五步业务状态码
                    if result.get("code") == 0:
                        data = result.get("data", {})

                        # 检查是否为高危账号
                        if data.get('status') == 2 and '高危异常行为' in str(data.get('message', '')):
                            self.log_signal.emit("🚨 检测到高危账号！")
                            high_risk_message = data.get('message', '账号存在高危异常行为')
                            high_risk_url = data.get('url', '')
                            self.log_signal.emit(f"⚠️ 高危信息: {high_risk_message}")
                            if high_risk_url:
                                self.log_signal.emit(f"🔗 处理链接: {high_risk_url}")
                            self.log_signal.emit("🛑 会话已停止，请处理高危问题后重试")
                            return {'success': False, 'error': '检测到高危账号', 'step5_data': result, 'high_risk': True}

                        self.emit_log("🎉 第五步老用户登录成功！", is_important=True)

                        # 显示完整的关键信息
                        token_info = data.get("token_info", {})
                        if token_info:
                            if "access_token" in token_info:
                                self.log_signal.emit(f"access_token：{token_info['access_token']}")

                            if "refresh_token" in token_info:
                                self.log_signal.emit(f"\nrefresh_token：{token_info['refresh_token']}")

                        # 显示完整的Cookie信息
                        cookie_info = data.get("cookie_info", {})
                        if cookie_info:
                            cookies = cookie_info.get("cookies", [])
                            # 将所有cookies连在一起，用分号分隔
                            cookie_parts = []
                            for cookie in cookies:
                                cookie_name = cookie.get("name", "")
                                cookie_value = cookie.get("value", "")
                                if cookie_name and cookie_value:
                                    cookie_parts.append(f"{cookie_name}={cookie_value}")
                            if cookie_parts:
                                self.log_signal.emit(f"\ncookies：{'; '.join(cookie_parts)}")

                        # 显示提示信息
                        self.log_signal.emit(f"\n💬 提示信息: 登录成功")

                        return {'success': True, 'data': result}
                    else:
                        error_msg = result.get('message', '未知错误')
                        self.log_signal.emit(f"❌ 第五步业务错误: {error_msg}")
                        return {'success': False, 'error': error_msg, 'data': result}

                except json.JSONDecodeError as e:
                    self.log_signal.emit(f"❌ 第五步响应JSON解析失败: {str(e)}")
                    self.log_signal.emit(f"📄 原始响应: {response.text}")
                    return {'success': False, 'error': 'JSON解析失败'}
            else:
                self.log_signal.emit(f"❌ 第五步HTTP请求失败: {response.status_code}")
                self.log_signal.emit(f"📄 错误内容: {response.text}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}

        except requests.exceptions.Timeout:
            self.log_signal.emit("⏰ 第五步请求超时")
            return {'success': False, 'error': '第五步请求超时'}
        except requests.exceptions.ConnectionError:
            self.log_signal.emit("🌐 第五步网络连接错误")
            return {'success': False, 'error': '第五步网络连接错误'}
        except Exception as e:
            self.log_signal.emit(f"💥 第五步未知错误: {str(e)}")
            return {'success': False, 'error': str(e)}

    def get_question_step6(self, account_info, first_step_headers=None):
        """
        第六步：获取答题题目
        根据用户提供的API规范实现获取题目接口
        """
        try:
            self.log_signal.emit("\n🚀 开始第六步获取答题题目...")

            # 从账号信息中获取必要的参数
            # 先尝试从data.data中获取（注册流程），再尝试从data中获取（登录流程）
            data_info = account_info.get('data', {})
            if 'data' in data_info:
                # 注册流程的数据结构
                inner_data = data_info.get('data', {})
                token_info = inner_data.get('token_info', {})
                cookie_info = inner_data.get('cookie_info', {})
            else:
                # 登录流程的数据结构
                token_info = data_info.get('token_info', {})
                cookie_info = data_info.get('cookie_info', {})

            self.log_signal.emit(f"🔍 调试信息 - token_info keys: {list(token_info.keys()) if token_info else 'None'}")
            self.log_signal.emit(f"🔍 调试信息 - cookie_info keys: {list(cookie_info.keys()) if cookie_info else 'None'}")

            access_token = token_info.get('access_token', '')
            if not access_token:
                self.log_signal.emit("❌ 缺少access_token，无法获取题目")
                self.log_signal.emit(f"🔍 完整数据结构: {json.dumps(account_info, ensure_ascii=False, indent=2)}")
                return {'success': False, 'error': '缺少access_token'}

            # 从cookies中获取bili_jct
            bili_jct = ''
            cookies = cookie_info.get('cookies', [])
            for cookie in cookies:
                if cookie.get('name') == 'bili_jct':
                    bili_jct = cookie.get('value', '')
                    break

            if not bili_jct:
                self.log_signal.emit("❌ 缺少bili_jct，无法获取题目")
                return {'success': False, 'error': '缺少bili_jct'}

            self.log_signal.emit(f"📋 使用答题参数:")
            self.log_signal.emit(f"  access_token: {access_token[:50]}...")
            self.log_signal.emit(f"  bili_jct: {bili_jct}")

            # 构建第六步请求参数（每次都重新生成时间戳）
            params = {
                "access_key": access_token,  # 这个就是账号access_token的值
                "appkey": "1d8b6e7d45233436",  # 固定  APPKEY对应的APPSEC值是560c52ccd288fed045859ed18bffd973 进行签名的
                "area": "0",  # 固定
                "csrf": bili_jct,  # 这个就是bili_jct的值
                "disable_rcmd": "0",  # 固定
                "image_version": "v",  # 固定
                "mobi_app": "android",  # 固定
                "platform": "h5",  # 固定
                "re_src": "0",  # 固定
                "statistics": json.dumps({
                    "appId": 1,
                    "platform": 3,
                    "version": "8.53.0",
                    "abtest": ""
                }, separators=(',', ':')),  # 固定
                "ts": self.api_utils.get_current_timestamp()  # 每次都重新生成时间戳
            }

            # 生成签名 - 使用答题专用的APPKEY和APPSEC
            answer_appkey = "1d8b6e7d45233436"
            answer_appsec = "560c52ccd288fed045859ed18bffd973"

            # 按照官方算法生成签名
            sorted_params = sorted(params.items())
            query_string = "&".join([f"{k}={v}" for k, v in sorted_params])
            sign_string = query_string + answer_appsec
            sign = hashlib.md5(sign_string.encode('utf-8')).hexdigest()
            params["sign"] = sign

            # 显示第六步请求参数
            self.log_signal.emit("\n📋 第六步请求参数:")
            for key, value in params.items():
                if key in ['access_key', 'csrf']:
                    # 敏感信息只显示前几位
                    display_value = str(value)[:20] + "..." if len(str(value)) > 20 else str(value)
                else:
                    display_value = str(value)
                self.log_signal.emit(f"  {key}: {display_value}")

            # 构建请求头 - 使用与第一步一致的设备信息
            # 如果有第一步的请求头，优先使用其中的设备相关字段
            if first_step_headers:
                self.log_signal.emit("🔄 使用第一步的设备信息构建答题请求头")
                base_headers = first_step_headers.copy()
            else:
                self.log_signal.emit("🔄 使用当前设备信息构建答题请求头")
                base_headers = self.api_utils.build_headers_for_path("/x/answer/v4/base", use_real_network_data=True, include_http2_pseudo_headers=False)

            headers = {
                ":method": "GET",  # 固定
                ":authority": "api.bilibili.com",  # 固定
                ":path": f"/x/answer/v4/base?{urlencode(params)}",  # 根据实际情况来
                ":scheme": "https",  # 固定
                "accept": "application/json, text/plain, */*",  # 固定
                "accept-encoding": "gzip, deflate, br",  # 固定
                "accept-language": "zh-CN",  # 固定
                "bili-http-engine": "ignet",  # 固定
                "buvid": self.api_utils.buvid,  # 要和第一步的一样(因为全程都要是同一个设备进行注册的)
                "cookie": self.build_cookie_string(cookie_info),  # 这个是cookie 后面加个一个Buvid=XUC511BCCD3BB16B95DA7A0D28B1556B525FD 这个就是第一步的
                "native_api_from": "h5",  # 固定
                "referer": "https://www.bilibili.com/h5/newbie/basic-1?score=0",  # 固定
                "user-agent": self.build_answer_user_agent(),  # 使用专门的答题user-agent构建方法
                "x-bili-aurora-eid": base_headers.get("x-bili-aurora-eid", "UlEFQFgHAVQCXE1RUlUIRQ=="),  # 这个根据官方的算法得来(具体和设备信息有关系)
                "x-bili-locale-bin": base_headers.get("x-bili-locale-bin", "Cg4KAnpoEgRIYW5zGgJDThIICgJ6aBoCQ04iDUFzaWEvU2hhbmdoYWkqBiswODowMA"),  # 要和第一步的一样(因为全程都要是同一个设备进行注册的)
                "x-bili-metadata-ip-region": "CN",  # 要和第一步的一样(因为全程都要是同一个设备进行的)
                "x-bili-mid": self.get_dedeuserid_from_cookies(cookie_info),  # 这个是DedeUserID的值，cookie里面的
                "x-bili-network-bin": base_headers.get("x-bili-network-bin", "CAEaBTQ2MDExKgUNAACAvw"),  # 要和第一步的一样(因为全程都要是同一个设备进行的)
                "x-bili-ticket": base_headers.get("x-bili-ticket", ""),  # 要和第一步的一样(因为全程都要是同一个设备进行的)
                "x-bili-trace-id": self.api_utils.generate_trace_id()  # 根据官方文档得api文档里面的算法得来(查阅官方文档跟手机设备的号型有关)
            }

            # 显示第六步请求头
            self.log_signal.emit("\n📋 第六步请求头详情:")
            for key, value in headers.items():
                if key == "cookie":
                    # cookie信息只显示前几位
                    display_value = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                else:
                    display_value = str(value)
                self.log_signal.emit(f"  {key}: {display_value}")

            # 发送第六步请求
            url = "https://api.bilibili.com/x/answer/v4/base"
            self.log_signal.emit(f"\n🌐 发送第六步获取题目请求到: {url}")

            # 使用TLS指纹会话发送请求
            tls_session = self.api_utils.get_tls_session()

            # 构建实际请求头（去掉HTTP/2伪头部）
            actual_headers = {k: v for k, v in headers.items() if not k.startswith(':')}

            if tls_session:
                response = tls_session.get(
                    url=url,
                    params=params,
                    headers=actual_headers,
                    timeout=30
                )
            else:
                # 备用方案：使用普通requests
                response = requests.get(
                    url=url,
                    params=params,
                    headers=actual_headers,
                    timeout=30
                )

            # 解析第六步响应
            self.log_signal.emit(f"📨 第六步响应状态码: {response.status_code}")

            # 显示响应头
            self.log_signal.emit("📋 第六步响应头:")
            for key, value in response.headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.log_signal.emit("✅ 第六步响应解析成功")
                    self.log_signal.emit(f"📄 第六步响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                    # 检查第六步业务状态码
                    if result.get("code") == 0:
                        self.log_signal.emit("🎉 第六步获取题目成功！")
                        return {'success': True, 'data': result, 'params': params, 'headers': actual_headers}
                    else:
                        error_msg = result.get('message', '未知错误')
                        self.log_signal.emit(f"❌ 第六步业务错误: {error_msg}")
                        return {'success': False, 'error': error_msg, 'data': result}

                except json.JSONDecodeError as e:
                    self.log_signal.emit(f"❌ 第六步响应JSON解析失败: {str(e)}")
                    self.log_signal.emit(f"📄 原始响应: {response.text}")
                    return {'success': False, 'error': 'JSON解析失败'}
            else:
                self.log_signal.emit(f"❌ 第六步HTTP请求失败: {response.status_code}")
                self.log_signal.emit(f"📄 错误内容: {response.text}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}

        except Exception as e:
            self.log_signal.emit(f"💥 第六步获取题目失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    def build_cookie_string(self, cookie_info):
        """构建cookie字符串"""
        try:
            cookies = cookie_info.get('cookies', [])
            cookie_parts = []

            # 添加所有cookies
            for cookie in cookies:
                cookie_name = cookie.get('name', '')
                cookie_value = cookie.get('value', '')
                if cookie_name and cookie_value:
                    cookie_parts.append(f"{cookie_name}={cookie_value}")

            # 添加Buvid
            cookie_parts.append(f"Buvid={self.api_utils.buvid}")

            return '; '.join(cookie_parts)
        except Exception as e:
            self.log_signal.emit(f"⚠️ 构建cookie字符串失败: {str(e)}")
            return f"Buvid={self.api_utils.buvid}"

    def get_dedeuserid_from_cookies(self, cookie_info):
        """从cookies中获取DedeUserID"""
        try:
            cookies = cookie_info.get('cookies', [])
            for cookie in cookies:
                if cookie.get('name') == 'DedeUserID':
                    return cookie.get('value', '')
            return ''
        except Exception as e:
            self.log_signal.emit(f"⚠️ 获取DedeUserID失败: {str(e)}")
            return ''

    def build_answer_user_agent(self):
        """
        构建答题专用的user-agent
        根据抓包数据构建符合真实手机设备的user-agent格式
        使用真实的浏览器版本参数，支持随机但一致的版本生成
        修正：移除末尾重复的设备信息，与真实设备抓包保持一致
        """
        try:
            # 获取设备信息
            device_info = self.api_utils.get_device_info()
            model = device_info.get('model', 'PJD110')
            android_version = device_info.get('android_version', '15')

            # 获取真实的浏览器信息
            browser_info = device_info.get('browser_info', {})
            mozilla_version = browser_info.get('mozilla_version', '5.0')
            webkit_version = browser_info.get('webkit_version', '537.36')
            webview_version = browser_info.get('webview_version', '4.0')
            chrome_version = browser_info.get('chrome_version', '137.0.7151.117')
            safari_version = browser_info.get('safari_version', '537.36')
            build_version = browser_info.get('build_version', 'AP3A.240617.008')
            sdk_int = browser_info.get('sdk_int', '35')

            # 构建符合抓包数据格式的user-agent，使用真实的浏览器版本
            # 注意：移除末尾重复的设备信息，与真实设备抓包保持一致
            user_agent = (
                f"Mozilla/{mozilla_version} (Linux; Android {android_version}; {model} Build/{build_version}; wv) "
                f"AppleWebKit/{webkit_version} (KHTML, like Gecko) Version/{webview_version} Chrome/{chrome_version} Mobile Safari/{safari_version} "
                f"os/android model/{model} build/8530200 osVer/{android_version} sdkInt/{sdk_int} network/2 "
                f"BiliApp/8530200 mobi_app/android channel/bili Buvid/{self.api_utils.buvid} "
                f"sessionID/{self.api_utils.session_id} innerVer/8530210 c_locale/zh-Hans_CN s_locale/zh_CN "
                f"disable_rcmd/0 themeId/1 sh/40 timezone/Asia/Shanghai utcOffset/+08:00 isDaylightTime/0 "
                f"alwaysTranslate/0 ipRegion/CN legalRegion/"
            )

            return user_agent
        except Exception as e:
            self.log_signal.emit(f"⚠️ 构建答题user-agent失败: {str(e)}")
            # 返回默认的user-agent（使用固定的真实版本）
            # 注意：移除末尾重复的设备信息，与真实设备抓包保持一致
            return (
                f"Mozilla/5.0 (Linux; Android 15; PJD110 Build/AP3A.240617.008; wv) "
                f"AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/137.0.7151.117 Mobile Safari/537.36 "
                f"os/android model/PJD110 build/8530200 osVer/15 sdkInt/35 network/2 "
                f"BiliApp/8530200 mobi_app/android channel/bili Buvid/{self.api_utils.buvid} "
                f"sessionID/{self.api_utils.session_id} innerVer/8530210 c_locale/zh-Hans_CN s_locale/zh_CN "
                f"disable_rcmd/0 themeId/1 sh/40 timezone/Asia/Shanghai utcOffset/+08:00 isDaylightTime/0 "
                f"alwaysTranslate/0 ipRegion/CN legalRegion/"
            )

    def submit_answer_step7(self, question_data, account_info, first_step_headers=None, captcha_token=None):
        """
        第七步：提交答案
        根据用户提供的API规范实现提交答案接口
        captcha_token: 第八步验证码验证成功后返回的token，用于后续请求
        """
        try:
            self.log_signal.emit("\n🚀 开始第七步提交答案...")

            # 调试：输出传入的题目数据
            self.log_signal.emit(f"🔍 调试信息 - question_data keys: {list(question_data.keys()) if question_data else 'None'}")
            if question_data and 'data' in question_data:
                self.log_signal.emit(f"🔍 调试信息 - question_data.data keys: {list(question_data['data'].keys())}")
                if 'question' in question_data['data']:
                    question_info = question_data['data']['question']
                    self.log_signal.emit(f"🔍 调试信息 - question keys: {list(question_info.keys())}")
                    self.log_signal.emit(f"🔍 调试信息 - question_id: {question_info.get('id', 'None')}")
                    self.log_signal.emit(f"🔍 调试信息 - options count: {len(question_info.get('options', []))}")

            # 从题目数据中获取必要信息
            question_info = question_data.get('data', {}).get('question', {})
            question_id = question_info.get('id', '')
            question_number = question_info.get('number', 1)
            options = question_info.get('options', [])

            if not question_id or not options:
                self.log_signal.emit("❌ 题目数据不完整，无法提交答案")
                self.log_signal.emit(f"🔍 调试信息 - question_id: '{question_id}', options: {options}")
                return {'success': False, 'error': '题目数据不完整'}

            self.log_signal.emit(f"📝 第{question_number}题 (ID: {question_id})")

            # 随机选择答案（因为目前只能随机选择）
            import random
            selected_option = random.choice(options)
            ans_key = selected_option.get('hash', '')

            if not ans_key:
                self.log_signal.emit("❌ 无法获取答案选项，提交失败")
                return {'success': False, 'error': '无法获取答案选项'}

            self.log_signal.emit(f"🎲 随机选择答案: {ans_key}")

            # 从账号信息中获取必要的参数
            # 先尝试从data.data中获取（注册流程），再尝试从data中获取（登录流程）
            data_info = account_info.get('data', {})
            if 'data' in data_info:
                # 注册流程的数据结构
                inner_data = data_info.get('data', {})
                token_info = inner_data.get('token_info', {})
                cookie_info = inner_data.get('cookie_info', {})
            else:
                # 登录流程的数据结构
                token_info = data_info.get('token_info', {})
                cookie_info = data_info.get('cookie_info', {})

            access_token = token_info.get('access_token', '')
            if not access_token:
                self.log_signal.emit("❌ 缺少access_token，无法提交答案")
                return {'success': False, 'error': '缺少access_token'}

            # 从cookies中获取bili_jct
            bili_jct = ''
            cookies = cookie_info.get('cookies', [])
            for cookie in cookies:
                if cookie.get('name') == 'bili_jct':
                    bili_jct = cookie.get('value', '')
                    break

            if not bili_jct:
                self.log_signal.emit("❌ 缺少bili_jct，无法提交答案")
                return {'success': False, 'error': '缺少bili_jct'}

            # 构建第七步请求参数（每次都重新生成时间戳）
            params = {
                "access_key": access_token,  # 这个就是账号access_token的值
                "ans_key": ans_key,  # 这个就是获取第六步里面获取到的题目的答案hash值
                "appkey": "1d8b6e7d45233436",  # 固定
                "area": "0",  # 固定
                "csrf": bili_jct,  # 这个就是bili_jct的值
                "disable_rcmd": "0",  # 固定
                "mobi_app": "android",  # 固定
                "platform": "h5",  # 固定
                "question_id": str(question_id),  # 是第六步响应体里面的id 跟答得题目有关
                "re_src": "0",  # 固定
                "statistics": json.dumps({
                    "appId": 1,
                    "platform": 3,
                    "version": "8.53.0",
                    "abtest": ""
                }, separators=(',', ':')),  # 固定
                "ts": self.api_utils.get_current_timestamp()  # 每次都重新生成时间戳
            }

            # 如果有验证码token，添加到请求参数中
            if captcha_token:
                params["token"] = captcha_token
                self.log_signal.emit(f"🔑 添加验证码token参数: {captcha_token}")
            else:
                self.log_signal.emit("ℹ️ 未提供验证码token，使用常规请求参数")

            # 生成签名 - 使用答题专用的APPKEY和APPSEC
            answer_appsec = "560c52ccd288fed045859ed18bffd973"

            # 按照官方算法生成签名
            sorted_params = sorted(params.items())
            query_string = "&".join([f"{k}={v}" for k, v in sorted_params])
            sign_string = query_string + answer_appsec
            sign = hashlib.md5(sign_string.encode('utf-8')).hexdigest()
            params["sign"] = sign

            # 显示第七步请求参数
            self.log_signal.emit("\n📋 第七步请求参数:")
            for key, value in params.items():
                if key in ['access_key', 'csrf']:
                    # 敏感信息只显示前几位
                    display_value = str(value)[:20] + "..." if len(str(value)) > 20 else str(value)
                else:
                    display_value = str(value)
                self.log_signal.emit(f"  {key}: {display_value}")

            # 构建请求头 - 使用与第一步一致的设备信息
            # 如果有第一步的请求头，优先使用其中的设备相关字段
            if first_step_headers:
                self.log_signal.emit("🔄 使用第一步的设备信息构建提交答案请求头")
                base_headers = first_step_headers.copy()
            else:
                self.log_signal.emit("🔄 使用当前设备信息构建提交答案请求头")
                base_headers = self.api_utils.build_headers_for_path("/x/answer/v4/base/check", use_real_network_data=True, include_http2_pseudo_headers=False)

            headers = {
                ":method": "POST",  # 固定
                ":authority": "api.bilibili.com",  # 固定
                ":path": "/x/answer/v4/base/check",  # 固定
                ":scheme": "https",  # 固定
                "accept": "application/json, text/plain, */*",  # 固定
                "accept-encoding": "gzip, deflate, br",  # 固定
                "accept-language": "zh-CN",  # 固定
                "bili-http-engine": "ignet",  # 固定
                "buvid": self.api_utils.buvid,  # 要和第一步的一样(因为全程都要是同一个设备进行注册的)
                "content-type": "application/x-www-form-urlencoded; charset=utf-8",  # 固定
                "cookie": self.build_cookie_string(cookie_info),  # 这个是cookie 后面加个一个Buvid=XUC511BCCD3BB16B95DA7A0D28B1556B525FD 这个就是第一步的
                "native_api_from": "h5",  # 固定
                "referer": "https://www.bilibili.com/h5/newbie/basic-1?score=0",  # 固定
                "user-agent": self.build_answer_user_agent(),  # 使用专门的答题user-agent构建方法
                "x-bili-aurora-eid": base_headers.get("x-bili-aurora-eid", "UlEFQFgHAVQCXE1RUlUIRQ=="),  # 和第六步的参数一样
                "x-bili-locale-bin": base_headers.get("x-bili-locale-bin", "Cg4KAnpoEgRIYW5zGgJDThIICgJ6aBoCQ04iDUFzaWEvU2hhbmdoYWkqBiswODowMA"),  # 要和第一步的一样(因为全程都要是同一个设备进行注册的)
                "x-bili-metadata-ip-region": "CN",  # 要和第一步的一样(因为全程都要是同一个设备进行的)
                "x-bili-mid": self.get_dedeuserid_from_cookies(cookie_info),  # 这个是DedeUserID的值，cookie里面的
                "x-bili-network-bin": base_headers.get("x-bili-network-bin", "CAEaBTQ2MDExKgUNAACAvw"),  # 要和第一步的一样(因为全程都要是同一个设备进行的)
                "x-bili-ticket": base_headers.get("x-bili-ticket", ""),  # 要和第一步的一样(因为全程都要是同一个设备进行的)
                "x-bili-trace-id": self.api_utils.generate_trace_id()  # 根据官方文档得api文档里面的算法得来(查阅官方文档跟手机设备的号型有关)
            }

            # 只在第60题时添加 x-bili-metadata-legal-region 参数
            if question_number == 60:
                legal_region = self.get_legal_region_from_country_code(self.country_code)
                headers["x-bili-metadata-legal-region"] = legal_region
                self.log_signal.emit(f"🌍 第{question_number}题添加法律地区参数: x-bili-metadata-legal-region = {legal_region}")

            # 构建请求体
            data = urlencode(params)
            content_length = str(len(data.encode('utf-8')))
            headers["content-length"] = content_length

            # 显示第七步请求头
            self.log_signal.emit("\n📋 第七步请求头详情:")
            for key, value in headers.items():
                if key == "cookie":
                    # cookie信息只显示前几位
                    display_value = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                else:
                    display_value = str(value)
                self.log_signal.emit(f"  {key}: {display_value}")

            # 发送第七步请求
            url = "https://api.bilibili.com/x/answer/v4/base/check"
            self.log_signal.emit(f"\n🌐 发送第七步提交答案请求到: {url}")
            self.log_signal.emit(f"📦 请求体长度: {len(data)} 字节")

            # 使用TLS指纹会话发送请求
            tls_session = self.api_utils.get_tls_session()

            # 构建实际请求头（去掉HTTP/2伪头部）
            actual_headers = {k: v for k, v in headers.items() if not k.startswith(':')}

            if tls_session:
                response = tls_session.post(
                    url=url,
                    data=data,
                    headers=actual_headers,
                    timeout=30
                )
            else:
                # 备用方案：使用普通requests
                response = requests.post(
                    url=url,
                    data=data,
                    headers=actual_headers,
                    timeout=30
                )

            # 解析第七步响应
            self.log_signal.emit(f"📨 第七步响应状态码: {response.status_code}")

            # 显示响应头
            self.log_signal.emit("📋 第七步响应头:")
            for key, value in response.headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.log_signal.emit("✅ 第七步响应解析成功")
                    self.log_signal.emit(f"📄 第七步响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                    # 检查第七步业务状态码
                    if result.get("code") == 0:
                        data_result = result.get("data", {})
                        passed = data_result.get("passed", False)
                        is_right = data_result.get("is_right", False)
                        ans_right = data_result.get("ans_right", "")

                        if passed and is_right:
                            self.log_signal.emit("🎉 第七步提交答案成功！答案正确！")
                        elif passed and not is_right:
                            self.log_signal.emit("✅ 第七步提交答案成功！但答案错误")
                        else:
                            self.log_signal.emit("❌ 第七步提交答案失败")

                        self.log_signal.emit(f"📊 答题结果: passed={passed}, is_right={is_right}")
                        self.log_signal.emit(f"📝 正确答案: {ans_right}")

                        return {'success': True, 'data': result, 'passed': passed, 'is_right': is_right}
                    elif result.get("code") == 41096:
                        # 检测到风控，需要进行第八步验证
                        self.log_signal.emit("🚨 检测到风控，需要进行验证码验证")
                        self.log_signal.emit(f"📄 风控响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

                        # 提取风控数据
                        data_result = result.get("data", {})
                        decisions = data_result.get("decisions", [])
                        grisk_id = data_result.get("grisk_id", "")
                        decision_ctx = data_result.get("decision_ctx", {})

                        if "verify_captcha" in decisions and grisk_id:
                            self.log_signal.emit("🔐 开始第八步验证码验证流程...")

                            # 调用第八步验证
                            step8_result = self.verify_captcha_step8(result, account_info, first_step_headers)

                            if step8_result.get('success'):
                                self.log_signal.emit("✅ 第八步验证码验证成功！")
                                # 从第八步结果中获取token
                                captcha_token = step8_result.get('token', '')
                                if captcha_token:
                                    self.log_signal.emit(f"🔑 获取到验证码token: {captcha_token}")
                                else:
                                    self.log_signal.emit("⚠️ 第八步未返回token")
                                # 验证成功后，重新提交答案，传递token
                                self.log_signal.emit("🔄 验证成功，重新提交答案...")
                                return self.submit_answer_step7(question_data, account_info, first_step_headers, captcha_token)
                            else:
                                self.log_signal.emit(f"❌ 第八步验证码验证失败: {step8_result.get('error', '未知错误')}")
                                return {'success': False, 'error': f'验证码验证失败: {step8_result.get("error", "未知错误")}', 'data': result, 'need_captcha': True}
                        else:
                            self.log_signal.emit("❌ 风控响应格式异常，无法处理")
                            return {'success': False, 'error': '风控响应格式异常', 'data': result, 'need_captcha': True}
                    else:
                        error_msg = result.get('message', '未知错误')
                        self.log_signal.emit(f"❌ 第七步业务错误: {error_msg}")
                        return {'success': False, 'error': error_msg, 'data': result}

                except json.JSONDecodeError as e:
                    self.log_signal.emit(f"❌ 第七步响应JSON解析失败: {str(e)}")
                    self.log_signal.emit(f"📄 原始响应: {response.text}")
                    return {'success': False, 'error': 'JSON解析失败'}
            else:
                self.log_signal.emit(f"❌ 第七步HTTP请求失败: {response.status_code}")
                self.log_signal.emit(f"📄 错误内容: {response.text}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}

        except Exception as e:
            self.log_signal.emit(f"💥 第七步提交答案失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    def verify_captcha_step8(self, risk_response, account_info, first_step_headers=None):
        """
        第八步：验证码验证
        当第七步触发风控时，需要进行验证码验证
        """
        try:
            self.log_signal.emit("\n🚀 开始第八步验证码验证...")

            # 从风控响应中提取必要信息
            data_result = risk_response.get("data", {})
            grisk_id = data_result.get("grisk_id", "")
            decision_ctx = data_result.get("decision_ctx", {})
            v_voucher = decision_ctx.get("v_voucher", "")

            if not grisk_id or not v_voucher:
                self.log_signal.emit("❌ 缺少必要的风控参数")
                return {'success': False, 'error': '缺少必要的风控参数'}

            self.log_signal.emit(f"🔍 grisk_id: {grisk_id}")
            self.log_signal.emit(f"🔍 v_voucher: {v_voucher}")

            # 从账号信息中获取必要的参数
            data_info = account_info.get('data', {})
            if 'data' in data_info:
                # 注册流程的数据结构
                inner_data = data_info.get('data', {})
                token_info = inner_data.get('token_info', {})
                cookie_info = inner_data.get('cookie_info', {})
            else:
                # 登录流程的数据结构
                token_info = data_info.get('token_info', {})
                cookie_info = data_info.get('cookie_info', {})

            # 从cookies中获取bili_jct
            bili_jct = ''
            cookies = cookie_info.get('cookies', [])
            for cookie in cookies:
                if cookie.get('name') == 'bili_jct':
                    bili_jct = cookie.get('value', '')
                    break

            if not bili_jct:
                self.log_signal.emit("❌ 缺少bili_jct，无法进行验证")
                return {'success': False, 'error': '缺少bili_jct'}

            # 构建json_payload
            json_payload = {
                "decisions": ["verify_captcha"],
                "grisk_id": grisk_id,
                "decision_ctx": decision_ctx
            }

            # 构建请求参数 - 使用动态生成的dm_track
            dm_track = self.api_utils.generate_dm_track()
            params = {
                "json_payload": json.dumps(json_payload, separators=(',', ':')),
                "dm_track": dm_track,
                "csrf": bili_jct
            }

            # 获取设备信息以生成动态的sec-ch-ua
            device_info = self.api_utils.get_device_info()
            browser_info = device_info.get('browser_info', {})
            chrome_version = browser_info.get('chrome_version', '137.0.7151.117')

            # 提取Chrome主版本号
            chrome_major_version = chrome_version.split('.')[0]

            # 动态生成sec-ch-ua，与Chrome版本保持一致
            sec_ch_ua = f"\"Android WebView\";v=\"{chrome_major_version}\", \"Chromium\";v=\"{chrome_major_version}\", \"Not/A)Brand\";v=\"24\""

            # 显示动态生成的sec-ch-ua信息
            self.log_signal.emit(f"🔧 动态生成sec-ch-ua: Chrome版本 {chrome_version} -> 主版本 {chrome_major_version}")
            self.log_signal.emit(f"📋 sec-ch-ua值: {sec_ch_ua}")

            # 构建请求头
            headers = {
                ":method": "POST",
                ":authority": "api.bilibili.com",
                ":path": "/x/gaia-vgate/v1/register",
                ":scheme": "https",
                "content-length": str(len(urllib.parse.urlencode(params).encode('utf-8'))),
                "sec-ch-ua-platform": "\"Android\"",
                "user-agent": self.build_answer_user_agent(),
                "sec-ch-ua": sec_ch_ua,
                "content-type": "application/x-www-form-urlencoded",
                "sec-ch-ua-mobile": "?1",
                "accept": "*/*",
                "origin": "https://www.bilibili.com",
                "x-requested-with": "tv.danmaku.bili",
                "sec-fetch-site": "same-site",
                "sec-fetch-mode": "cors",
                "sec-fetch-dest": "empty",
                "referer": "https://www.bilibili.com/",
                "accept-encoding": "gzip, deflate, br, zstd",
                "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                "cookie": f"buvid4={self.api_utils.buvid4}",
                "priority": "u=1, i"
            }

            # 显示第八步请求参数
            self.log_signal.emit("\n📋 第八步请求参数:")
            for key, value in params.items():
                if key == "json_payload":
                    self.log_signal.emit(f"  {key}: {value}")
                elif key == "dm_track":
                    display_value = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                    self.log_signal.emit(f"  {key}: {display_value}")
                else:
                    self.log_signal.emit(f"  {key}: {value}")

            # 构建请求体
            data = urllib.parse.urlencode(params)
            content_length = str(len(data.encode('utf-8')))
            headers["content-length"] = content_length

            # 显示第八步请求头
            self.log_signal.emit("\n📋 第八步请求头详情:")
            for key, value in headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            # 发送第八步请求
            url = "https://api.bilibili.com/x/gaia-vgate/v1/register"
            self.log_signal.emit(f"\n🌐 发送第八步验证请求到: {url}")
            self.log_signal.emit(f"📦 请求体长度: {len(data)} 字节")

            # 使用TLS指纹会话发送请求
            tls_session = self.api_utils.get_tls_session()

            # 构建实际请求头（去掉HTTP/2伪头部）
            actual_headers = {k: v for k, v in headers.items() if not k.startswith(':')}

            if tls_session:
                response = tls_session.post(
                    url=url,
                    data=data,
                    headers=actual_headers,
                    timeout=30
                )
            else:
                # 备用方案：使用普通requests
                response = requests.post(
                    url=url,
                    data=data,
                    headers=actual_headers,
                    timeout=30
                )

            # 解析第八步响应
            self.log_signal.emit(f"📨 第八步响应状态码: {response.status_code}")

            # 显示响应头
            self.log_signal.emit("📋 第八步响应头:")
            for key, value in response.headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.log_signal.emit("✅ 第八步响应解析成功")
                    self.log_signal.emit(f"📄 第八步响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                    # 检查第八步业务状态码
                    if result.get("code") == 0:
                        self.log_signal.emit("🎉 第八步验证码验证成功！")
                        data_result = result.get("data", {})

                        # 检查验证类型
                        verify_type = data_result.get("type", "")
                        if verify_type == "geetest":
                            # 极验验证码
                            token = data_result.get("token", "")
                            geetest_data = data_result.get("geetest", {})
                            challenge = geetest_data.get("challenge", "")
                            gt = geetest_data.get("gt", "")

                            self.log_signal.emit(f"🔐 验证类型: {verify_type}")
                            self.log_signal.emit(f"🔑 token: {token}")
                            self.log_signal.emit(f"🔑 challenge: {challenge}")
                            self.log_signal.emit(f"🔑 gt: {gt}")

                            # 这里可以调用打码服务处理极验验证码
                            # 暂时返回成功，实际应用中需要处理验证码
                            return {'success': True, 'data': result, 'type': verify_type, 'token': token, 'geetest': geetest_data}
                        else:
                            self.log_signal.emit(f"🔐 验证类型: {verify_type}")
                            return {'success': True, 'data': result, 'type': verify_type}
                    else:
                        error_msg = result.get('message', '未知错误')
                        self.log_signal.emit(f"❌ 第八步业务错误: {error_msg}")
                        return {'success': False, 'error': error_msg, 'data': result}

                except json.JSONDecodeError as e:
                    self.log_signal.emit(f"❌ 第八步响应JSON解析失败: {str(e)}")
                    self.log_signal.emit(f"📄 原始响应: {response.text}")
                    return {'success': False, 'error': 'JSON解析失败'}
            else:
                self.log_signal.emit(f"❌ 第八步HTTP请求失败: {response.status_code}")
                self.log_signal.emit(f"📄 错误内容: {response.text}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}

        except Exception as e:
            self.log_signal.emit(f"💥 第八步验证码验证失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    def submit_exam_step9(self, account_info, first_step_headers=None):
        """
        第九步：答完60题后提交试卷
        根据用户提供的API规范实现提交试卷接口
        """
        try:
            self.log_signal.emit("\n🚀 开始第九步提交试卷...")

            # 从账号信息中获取必要的参数
            data_info = account_info.get('data', {})
            if 'data' in data_info:
                # 注册流程的数据结构
                inner_data = data_info.get('data', {})
                token_info = inner_data.get('token_info', {})
                cookie_info = inner_data.get('cookie_info', {})
            else:
                # 登录流程的数据结构
                token_info = data_info.get('token_info', {})
                cookie_info = data_info.get('cookie_info', {})

            # 获取access_token
            access_token = token_info.get('access_token', '')
            if not access_token:
                self.log_signal.emit("❌ 缺少access_token，无法提交试卷")
                return {'success': False, 'error': '缺少access_token'}

            # 从cookies中获取必要的cookie值
            cookies_dict = {}
            cookies = cookie_info.get('cookies', [])
            for cookie in cookies:
                cookie_name = cookie.get('name', '')
                cookie_value = cookie.get('value', '')
                if cookie_name and cookie_value:
                    cookies_dict[cookie_name] = cookie_value

            # 获取必要的cookie值
            sessdata = cookies_dict.get('SESSDATA', '')
            bili_jct = cookies_dict.get('bili_jct', '')
            dede_user_id = cookies_dict.get('DedeUserID', '')
            dede_user_id_ckmd5 = cookies_dict.get('DedeUserID__ckMd5', '')
            sid = cookies_dict.get('sid', '')

            if not all([sessdata, bili_jct, dede_user_id]):
                self.log_signal.emit("❌ 缺少必要的cookie信息，无法提交试卷")
                return {'success': False, 'error': '缺少必要的cookie信息'}

            self.log_signal.emit("📋 使用提交试卷数据:")
            self.log_signal.emit(f"  access_token: {access_token[:50]}...")
            self.log_signal.emit(f"  SESSDATA: {sessdata[:50]}...")
            self.log_signal.emit(f"  bili_jct: {bili_jct}")
            self.log_signal.emit(f"  DedeUserID: {dede_user_id}")

            # 构建第九步请求参数
            params = {
                "access_key": access_token,  # 这个就是账号access_token的值
                "appkey": self.api_utils.APPKEY,  # 固定  APPKEY对应的APPSEC值是560c52ccd288fed045859ed18bffd973 进行签名的
                "area": "0",  # 固定
                "csrf": bili_jct,  # 这个就是bili_jct的值
                "disable_rcmd": "0",  # 固定
                "mobi_app": "android",  # 固定
                "platform": "android",  # 固定
                "re_src": "0",  # 固定
                "statistics": json.dumps({
                    "appId": 1,
                    "platform": 3,
                    "version": "8.53.0",
                    "abtest": ""
                }, separators=(',', ':')),  # 固定
                "ts": self.api_utils.get_current_timestamp(),  # 根据官方文档得api文档里面的算法得来(查阅官方文档当前时间戳)
            }

            # 生成签名
            params["sign"] = self.api_utils.generate_sign(params.copy())

            # 构建请求头 - 根据用户提供的规范
            # 获取legal_region
            legal_region = self.get_legal_region_from_country_code(self.country_code)

            headers = {
                ":method": "POST",  # 固定
                ":authority": "api.bilibili.com",  # 固定
                ":path": "/x/answer/v4/submit",  # 固定
                ":scheme": "https",  # 固定
                "accept": "application/json, text/plain, */*",  # 固定
                "accept-encoding": "gzip, deflate, br",  # 固定
                "accept-language": "zh-CN",  # 固定
                "bili-http-engine": "ignet",  # 固定
                "buvid": self.api_utils.buvid,  # 要和第一步的一样(因为全程都要是同一个设备进行的)
                "content-type": "application/x-www-form-urlencoded; charset=utf-8",  # 固定
                "native_api_from": "h5",  # 固定
                "referer": "https://www.bilibili.com/h5/newbie/optional-entry?score=60",  # 固定
                "user-agent": self.build_answer_user_agent(),  # 参考这个根据实际情况来
                "x-bili-aurora-eid": self.api_utils.generate_aurora_eid(),  # 和第六步的参数一样
                "x-bili-locale-bin": self.api_utils.generate_locale_bin(),  # 根据实际情况来和前面的一样(全程都是通过个设备进行的)
                "x-bili-metadata-ip-region": "CN",  # 要和第一步的一样(因为全程都要是同一个设备进行的)
                "x-bili-metadata-legal-region": legal_region,  # 这个要加是根据号码的归属地来的
                "x-bili-mid": dede_user_id,  # 这个是DedeUserID的值，cookie里面的
                "x-bili-network-bin": self.api_utils.generate_network_bin(),  # 要和第一步的一样(因为全程都要是同一个设备进行的)
                "x-bili-ticket": first_step_headers.get("x-bili-ticket", "") if first_step_headers else self.api_utils.generate_bili_ticket(),  # 要和第一步的一样(因为全程都要是同一个设备进行的)
                "x-bili-trace-id": self.api_utils.generate_trace_id()  # 根据官方文档得api文档里面的算法得来(查阅官方文档跟手机设备的号型有关)
            }

            # 显示第九步x-bili-ticket使用情况
            if first_step_headers and 'x-bili-ticket' in first_step_headers:
                self.log_signal.emit("🔄 第九步使用第一步相同的x-bili-ticket")
                self.log_signal.emit(f"🔄 第一步ticket: {first_step_headers['x-bili-ticket'][:50]}...")
                self.log_signal.emit(f"🔄 第九步ticket: {headers['x-bili-ticket'][:50]}...")
            else:
                self.log_signal.emit("⚠️ 第九步未提供第一步请求头，将使用新生成的x-bili-ticket")

            # 构建cookies字符串
            cookie_parts = [
                f"SESSDATA={sessdata}",
                f"bili_jct={bili_jct}",
                f"DedeUserID={dede_user_id}",
                f"DedeUserID__ckMd5={dede_user_id_ckmd5}",
                f"sid={sid}",
                f"Buvid={self.api_utils.buvid}"  # 要和第一步的一样(因为全程都要是同一个设备进行的)
            ]
            cookie_string = "; ".join(cookie_parts)
            headers["cookie"] = cookie_string

            # 构建请求体
            data = urlencode(params)
            content_length = str(len(data.encode('utf-8')))
            headers["content-length"] = content_length

            # 显示第九步请求参数
            self.log_signal.emit("\n📋 第九步请求参数:")
            for key, value in params.items():
                if key == "access_key":
                    display_value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                else:
                    display_value = str(value)
                self.log_signal.emit(f"  {key}: {display_value}")

            # 显示第九步请求头（包含HTTP/2伪头部用于调试）
            self.log_signal.emit("\n📋 第九步请求头详情:")
            for key, value in headers.items():
                if key == "cookie":
                    display_value = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                else:
                    display_value = str(value)
                self.log_signal.emit(f"  {key}: {display_value}")

            # 发送第九步请求
            url = "https://api.bilibili.com/x/answer/v4/submit"
            self.log_signal.emit(f"\n🌐 发送第九步提交试卷请求到: {url}")
            self.log_signal.emit(f"📦 请求体长度: {len(data)} 字节")

            # 使用TLS指纹会话发送请求
            tls_session = self.api_utils.get_tls_session()

            # 构建实际请求头（去掉HTTP/2伪头部）
            actual_headers = {k: v for k, v in headers.items() if not k.startswith(':')}

            if tls_session:
                response = tls_session.post(
                    url=url,
                    data=data,
                    headers=actual_headers,
                    timeout=30
                )
            else:
                # 备用方案：使用普通requests
                response = requests.post(
                    url=url,
                    data=data,
                    headers=actual_headers,
                    timeout=30
                )

            # 解析第九步响应
            self.log_signal.emit(f"📨 第九步响应状态码: {response.status_code}")

            # 显示响应头
            self.log_signal.emit("📋 第九步响应头:")
            for key, value in response.headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.log_signal.emit("✅ 第九步响应解析成功")
                    self.log_signal.emit(f"📄 第九步响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                    # 检查第九步业务状态码
                    if result.get("code") == 0:
                        self.log_signal.emit("🎉 第九步提交试卷成功！")
                        data_result = result.get("data", {})

                        # 显示试卷提交结果
                        hid = data_result.get("hid", 0)
                        mid = data_result.get("mid", 0)
                        score = data_result.get("score", 0)
                        status = data_result.get("status", 0)
                        number = data_result.get("number", 0)
                        exam_result = data_result.get("result", "")
                        stage = data_result.get("stage", "")
                        version = data_result.get("version", "")
                        start_time = data_result.get("start_time", 0)
                        first_answer = data_result.get("first_answer", 0)
                        in_reg_audit = data_result.get("in_reg_audit", False)
                        edition = data_result.get("edition", 0)
                        rewards = data_result.get("rewards", None)
                        must_right_num = data_result.get("must_right_num", 0)
                        base_err_num = data_result.get("base_err_num", 0)
                        only_member_passed = data_result.get("only_member_passed", False)
                        score_tip = data_result.get("score_tip", "")

                        self.log_signal.emit(f"📊 试卷ID: {hid}")
                        self.log_signal.emit(f"📊 用户ID: {mid}")
                        self.log_signal.emit(f"📊 最终得分: {score}")
                        self.log_signal.emit(f"📊 考试状态: {status}")
                        self.log_signal.emit(f"📊 题目编号: {number}")
                        self.log_signal.emit(f"📊 考试结果: {exam_result}")
                        self.log_signal.emit(f"📊 考试阶段: {stage}")
                        self.log_signal.emit(f"📊 版本: {version}")
                        self.log_signal.emit(f"📊 开始时间: {start_time}")
                        self.log_signal.emit(f"📊 首次答题: {first_answer}")
                        self.log_signal.emit(f"📊 注册审核: {in_reg_audit}")
                        self.log_signal.emit(f"📊 版本号: {edition}")
                        self.log_signal.emit(f"📊 奖励: {rewards}")
                        self.log_signal.emit(f"📊 必答题数: {must_right_num}")
                        self.log_signal.emit(f"📊 基础错误数: {base_err_num}")
                        self.log_signal.emit(f"📊 仅会员通过: {only_member_passed}")
                        self.log_signal.emit(f"📊 得分提示: {score_tip}")

                        # 根据响应判断是否交卷成功
                        if exam_result == "upgrade" and stage == "result":
                            self.log_signal.emit("🎉 交卷成功！考试通过，账号升级成功！")
                        else:
                            self.log_signal.emit("✅ 交卷成功！")

                        return {'success': True, 'data': result}
                    else:
                        error_msg = result.get('message', '未知错误')
                        self.log_signal.emit(f"❌ 第九步业务错误: {error_msg}")
                        return {'success': False, 'error': error_msg, 'data': result}

                except json.JSONDecodeError as e:
                    self.log_signal.emit(f"❌ 第九步响应JSON解析失败: {str(e)}")
                    self.log_signal.emit(f"📄 原始响应: {response.text}")
                    return {'success': False, 'error': 'JSON解析失败'}
            else:
                self.log_signal.emit(f"❌ 第九步HTTP请求失败: {response.status_code}")
                self.log_signal.emit(f"📄 错误内容: {response.text}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}

        except Exception as e:
            self.log_signal.emit(f"💥 第九步提交试卷失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    def check_user_level_step10(self, account_info, first_step_headers=None):
        """
        第十步：检查老用户等级
        如果是老用户则进行第十步，看老用户的等级是多少(响应level里面的值是等级)
        如果level的值等于或低于0级则继续进行后面的步骤直到结束
        如果高于0级那就保存到账号记录管理里面并在记录类型里面显示老用户多少等级
        access token refresh token cookies这三个值也要保存要完整
        """
        try:
            self.log_signal.emit("\n=== 第十步：检查老用户等级 ===")

            # 从account_info中提取必要信息
            access_token = ""
            refresh_token = ""
            cookies = ""
            mid = ""

            # 从不同的数据结构中提取信息
            if 'step5_data' in account_info:
                # 第五步登录的数据
                step5_data = account_info['step5_data']

                # step5_data现在保存的是完整的step5_result，需要进一步提取data字段
                if isinstance(step5_data, dict) and 'data' in step5_data:
                    # step5_data是完整的step5_result，需要提取 data.data 字段
                    outer_data = step5_data['data']

                    # 进一步提取内层的data字段
                    if isinstance(outer_data, dict) and 'data' in outer_data:
                        data = outer_data['data']

                        token_info = data.get("token_info", {})
                        access_token = token_info.get("access_token", "")
                        refresh_token = token_info.get("refresh_token", "")
                        mid = str(token_info.get("mid", ""))

                        # 提取cookie信息
                        cookie_info = data.get("cookie_info", {})
                        if cookie_info:
                            cookies_list = cookie_info.get("cookies", [])
                            cookie_parts = []
                            for cookie in cookies_list:
                                cookie_name = cookie.get("name", "")
                                cookie_value = cookie.get("value", "")
                                if cookie_name and cookie_value:
                                    cookie_parts.append(f"{cookie_name}={cookie_value}")
                            cookies = "; ".join(cookie_parts)
            elif 'data' in account_info:
                # 直接的数据结构
                data = account_info['data']
                token_info = data.get("token_info", {})
                access_token = token_info.get("access_token", "")
                refresh_token = token_info.get("refresh_token", "")
                mid = str(token_info.get("mid", ""))

                # 提取cookie信息
                cookie_info = data.get("cookie_info", {})
                if cookie_info:
                    cookies_list = cookie_info.get("cookies", [])
                    cookie_parts = []
                    for cookie in cookies_list:
                        cookie_name = cookie.get("name", "")
                        cookie_value = cookie.get("value", "")
                        if cookie_name and cookie_value:
                            cookie_parts.append(f"{cookie_name}={cookie_value}")
                    cookies = "; ".join(cookie_parts)

            if not access_token:
                self.log_signal.emit("❌ 第十步失败：未找到access_token")
                return {'success': False, 'error': '未找到access_token'}

            if not mid:
                self.log_signal.emit("❌ 第十步失败：未找到用户ID(mid)")
                return {'success': False, 'error': '未找到用户ID'}

            self.log_signal.emit(f"✓ 提取到access_token: {access_token[:20]}...")
            self.log_signal.emit(f"✓ 提取到用户ID: {mid}")

            # 构建第十步请求参数
            params = self.api_utils.build_myinfo_params(access_token)
            self.log_signal.emit("✓ 第十步请求参数构建完成")

            # 显示第十步请求参数
            self.log_signal.emit("\n📋 第十步请求参数:")
            for key, value in params.items():
                if key in ['access_key']:
                    display_value = str(value)[:20] + "..." if len(str(value)) > 20 else str(value)
                else:
                    display_value = str(value)
                self.log_signal.emit(f"  {key}: {display_value}")

            # 构建第十步请求头
            headers = self.api_utils.build_myinfo_headers(access_token, mid)
            self.log_signal.emit("✓ 第十步请求头构建完成")

            # 显示第十步请求头（包含HTTP/2伪头部用于调试）
            self.log_signal.emit("\n📋 第十步请求头详情:")
            for key, value in headers.items():
                if key in ['x-bili-mid']:
                    display_value = str(value)
                elif key.startswith(':path'):
                    display_value = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                else:
                    display_value = str(value)
                self.log_signal.emit(f"  {key}: {display_value}")

            # 发送第十步请求
            url = "https://app.bilibili.com/x/v2/account/myinfo"
            self.log_signal.emit(f"\n🌐 发送第十步获取用户信息请求到: {url}")

            # 使用TLS指纹会话发送请求
            tls_session = self.api_utils.get_tls_session()

            # 构建实际请求头（去掉HTTP/2伪头部）
            actual_headers = {k: v for k, v in headers.items() if not k.startswith(':')}

            if tls_session:
                response = tls_session.get(
                    url=url,
                    params=params,
                    headers=actual_headers,
                    timeout=30
                )
            else:
                # 备用方案：使用普通requests
                import requests
                response = requests.get(
                    url=url,
                    params=params,
                    headers=actual_headers,
                    timeout=30
                )

            # 解析第十步响应
            self.log_signal.emit(f"📨 第十步响应状态码: {response.status_code}")

            # 显示响应头
            self.log_signal.emit("📋 第十步响应头:")
            for key, value in response.headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.log_signal.emit("✅ 第十步响应解析成功")
                    self.log_signal.emit(f"📄 第十步响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                    # 检查第十步业务状态码
                    if result.get("code") == 0:
                        self.log_signal.emit("🎉 第十步获取用户信息成功！")
                        data_result = result.get("data", {})

                        # 提取用户等级信息
                        user_level = data_result.get("level", 0)
                        user_name = data_result.get("name", "")
                        user_mid = data_result.get("mid", 0)
                        user_coins = data_result.get("coins", 0)
                        user_sign = data_result.get("sign", "")

                        self.log_signal.emit(f"👤 用户名: {user_name}")
                        self.log_signal.emit(f"🆔 用户ID: {user_mid}")
                        self.log_signal.emit(f"⭐ 用户等级: {user_level}")
                        self.log_signal.emit(f"💰 硬币数: {user_coins}")
                        self.log_signal.emit(f"📝 个性签名: {user_sign}")

                        # 根据用户等级决定后续流程
                        # 所有用户都按照≥0级处理（根据是否勾选答题决定）
                        self.log_signal.emit(f"✅ 用户等级为{user_level}级，需要根据答题设置决定后续流程")
                        return {
                            'success': True,
                            'continue_flow': True,
                            'level': user_level,
                            'user_info': data_result,
                            'access_token': access_token,
                            'refresh_token': refresh_token,
                            'cookies': cookies,
                            'is_level_gte_zero': True  # 标记为需要特殊处理的用户
                        }

                    else:
                        error_msg = result.get('message', '未知错误')
                        self.log_signal.emit(f"❌ 第十步业务错误: {error_msg}")
                        return {'success': False, 'error': error_msg, 'data': result}

                except json.JSONDecodeError as e:
                    self.log_signal.emit(f"❌ 第十步响应JSON解析失败: {str(e)}")
                    self.log_signal.emit(f"📄 原始响应: {response.text}")
                    return {'success': False, 'error': 'JSON解析失败'}
            else:
                self.log_signal.emit(f"❌ 第十步HTTP请求失败: {response.status_code}")
                self.log_signal.emit(f"📄 错误内容: {response.text}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}

        except requests.exceptions.RequestException as e:
            self.log_signal.emit(f"🌐 第十步网络连接错误: {str(e)}")
            return {'success': False, 'error': '第十步网络连接错误'}
        except Exception as e:
            self.log_signal.emit(f"💥 第十步未知错误: {str(e)}")
            return {'success': False, 'error': str(e)}

    def auto_answer_questions(self, account_info, first_step_headers=None):
        """
        自动答题流程：根据题目number字段循环答题，直到number达到60且第60题答对
        """
        try:
            self.log_signal.emit("\n🎯 开始自动答题流程，目标：60题且第60题答对")
            self.log_signal.emit(f"⏱️ 答题间隔：{self.answer_interval}秒")

            target_questions = 60
            correct_answers = 0
            wrong_answers = 0
            current_question_number = 0
            attempt_count = 0
            max_attempts = 1000  # 防止无限循环的最大尝试次数
            question_60_passed = False  # 标记第60题是否答对

            while (current_question_number < target_questions or not question_60_passed) and attempt_count < max_attempts:
                attempt_count += 1
                try:
                    self.log_signal.emit(f"\n📝 正在获取题目... (尝试第{attempt_count}次)")

                    # 第六步：获取题目
                    step6_result = self.get_question_step6(account_info, first_step_headers)
                    if not step6_result.get('success'):
                        self.log_signal.emit(f"❌ 获取题目失败: {step6_result.get('error', '未知错误')}")
                        continue

                    # 解析题目数据 - step6_result['data']是完整的API响应
                    api_response = step6_result.get('data', {})
                    question_data = api_response.get('data', {})
                    question_info = question_data.get('question', {})
                    current_question_number = question_info.get('number', 0)

                    if current_question_number == 0:
                        self.log_signal.emit("⚠️ 题目编号为0，跳过此题")
                        continue

                    self.log_signal.emit(f"📋 第{current_question_number}题获取成功")

                    # 检查是否已达到目标题数
                    if current_question_number > target_questions:
                        self.log_signal.emit(f"🎉 已超过目标题数{target_questions}，停止答题")
                        break

                    # 等待答题间隔
                    if self.answer_interval > 0:
                        self.log_signal.emit(f"⏳ 等待{self.answer_interval}秒后提交答案...")
                        import time
                        time.sleep(self.answer_interval)

                    # 第七步：提交答案 - 传递完整的API响应
                    step7_result = self.submit_answer_step7(api_response, account_info, first_step_headers)
                    if step7_result.get('success'):
                        is_right = step7_result.get('is_right', False)
                        if is_right:
                            correct_answers += 1
                            self.log_signal.emit(f"✅ 第{current_question_number}题答对了！")
                        else:
                            wrong_answers += 1
                            self.log_signal.emit(f"❌ 第{current_question_number}题答错了")

                        # 显示当前进度
                        progress_percentage = (current_question_number / target_questions * 100)
                        self.log_signal.emit(f"📊 当前进度: {current_question_number}/{target_questions} ({progress_percentage:.1f}%) (正确:{correct_answers}, 错误:{wrong_answers})")

                        # 检查是否已完成60题
                        if current_question_number >= target_questions:
                            step7_is_right = step7_result.get('is_right', False)
                            if step7_is_right:
                                question_60_passed = True
                                self.log_signal.emit(f"🎉 恭喜！已完成第{target_questions}题且答对了(is_right=True)！")
                                break
                            else:
                                self.log_signal.emit(f"⚠️ 已达到第{target_questions}题但答错了(is_right=False)，继续答题直到答对...")
                                # 继续答题直到第60题答对，不设置question_60_passed为True
                    else:
                        self.log_signal.emit(f"❌ 第{current_question_number}题提交失败: {step7_result.get('error', '未知错误')}")

                except Exception as e:
                    self.log_signal.emit(f"💥 第{attempt_count}次尝试处理异常: {str(e)}")
                    continue

            # 检查是否因为达到最大尝试次数而退出
            if attempt_count >= max_attempts:
                self.log_signal.emit(f"⚠️ 已达到最大尝试次数({max_attempts})，停止答题")

            # 显示最终统计
            actual_questions = max(current_question_number, correct_answers + wrong_answers)
            self.log_signal.emit(f"\n📈 答题统计:")
            self.log_signal.emit(f"  目标题数: {target_questions}")
            self.log_signal.emit(f"  实际完成: {actual_questions}")
            self.log_signal.emit(f"  正确数: {correct_answers}")
            self.log_signal.emit(f"  错误数: {wrong_answers}")
            if actual_questions > 0:
                self.log_signal.emit(f"  正确率: {(correct_answers / actual_questions * 100):.1f}%")
            else:
                self.log_signal.emit(f"  正确率: 0.0%")

            # 检查是否完成了60题且第60题答对，如果是则尝试提交试卷
            submit_result = None
            rename_result = None
            if current_question_number >= target_questions and question_60_passed:
                self.log_signal.emit("\n🚀 开始第九步：提交试卷...")
                submit_result = self.submit_exam_step9(account_info, first_step_headers)
                if submit_result.get('success'):
                    self.log_signal.emit("🎉 第九步提交试卷成功！")
                    submit_data = submit_result.get('data', {}).get('data', {})
                    self.log_signal.emit(f"📊 最终得分: {submit_data.get('score', 0)}")
                    self.log_signal.emit(f"📊 考试状态: {submit_data.get('status', 'unknown')}")
                    self.log_signal.emit(f"📊 考试结果: {submit_data.get('result', 'unknown')}")

                    # 如果勾选了随机改名，执行第十一步
                    if self.auto_rename:
                        self.log_signal.emit("\n🎲 检测到随机改名已启用，开始第十一步...")
                        rename_result = self.random_rename_step11(account_info, first_step_headers)
                        if rename_result.get('success'):
                            self.log_signal.emit("🎉 第十一步随机改名成功！")
                            new_username = rename_result.get('new_username', '')
                            self.log_signal.emit(f"✨ 用户名已修改为: {new_username}")
                        else:
                            self.log_signal.emit(f"❌ 第十一步随机改名失败: {rename_result.get('error', '未知错误')}")
                    else:
                        self.log_signal.emit("ℹ️ 未勾选随机改名，跳过第十一步")
                else:
                    self.log_signal.emit(f"❌ 第九步提交试卷失败: {submit_result.get('error', '未知错误')}")
            elif current_question_number >= target_questions and not question_60_passed:
                self.log_signal.emit(f"⚠️ 已完成{target_questions}题但第{target_questions}题未答对，无法提交试卷")
            else:
                self.log_signal.emit(f"⚠️ 未完成{target_questions}题，无法提交试卷")

            result_data = {
                'target_questions': target_questions,
                'actual_questions': actual_questions,
                'correct_answers': correct_answers,
                'wrong_answers': wrong_answers,
                'accuracy': (correct_answers / actual_questions * 100) if actual_questions > 0 else 0,
                'question_60_passed': question_60_passed
            }

            # 如果有提交试卷的结果，添加到返回数据中
            if submit_result:
                result_data['submit_result'] = submit_result

            # 如果有随机改名的结果，添加到返回数据中
            if rename_result:
                result_data['rename_result'] = rename_result

            return {
                'success': True,
                'data': result_data
            }

        except Exception as e:
            self.log_signal.emit(f"💥 自动答题流程失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    def generate_random_username(self):
        """
        生成随机用户名：6个中文汉字 + 3个英文字母（随机大小写）
        """
        import random

        # 常用中文汉字列表（避免生僻字和违规词汇）
        chinese_chars = [
            '春', '夏', '秋', '冬', '东', '南', '西', '北', '上', '下',
            '左', '右', '前', '后', '中', '内', '外', '高', '低', '大',
            '小', '长', '短', '宽', '窄', '深', '浅', '厚', '薄', '重',
            '轻', '快', '慢', '新', '旧', '好', '坏', '美', '丑', '亮',
            '暗', '红', '橙', '黄', '绿', '青', '蓝', '紫', '白', '黑',
            '灰', '粉', '金', '银', '铜', '铁', '木', '水', '火', '土',
            '风', '雨', '雪', '云', '雾', '霜', '露', '冰', '热', '冷',
            '温', '凉', '暖', '寒', '晴', '阴', '多', '少', '满', '空',
            '圆', '方', '尖', '平', '直', '弯', '粗', '细', '光', '滑',
            '糙', '软', '硬', '松', '紧', '干', '湿', '净', '脏', '香',
            '臭', '甜', '苦', '酸', '辣', '咸', '淡', '鲜', '嫩', '老',
            '生', '熟', '活', '死', '动', '静', '忙', '闲', '早', '晚',
            '远', '近', '里', '外', '开', '关', '进', '出', '来', '去',
            '回', '到', '从', '向', '往', '朝', '对', '跟', '和', '与',
            '同', '异', '像', '似', '如', '若', '比', '较', '更', '最',
            '很', '太', '非', '挺', '够', '还', '再', '又', '也', '都',
            '只', '就', '才', '刚', '正', '在', '着', '了', '过', '起',
            '来', '去', '下', '上', '出', '进', '回', '到', '从', '向',
            '花', '草', '树', '叶', '果', '根', '枝', '干', '皮', '籽',
            '鸟', '鱼', '虫', '兽', '猫', '狗', '马', '牛', '羊', '猪',
            '鸡', '鸭', '鹅', '兔', '鼠', '虎', '龙', '蛇', '猴', '鹿',
            '象', '熊', '狼', '狐', '鹰', '鸽', '燕', '雀', '鸦', '鹤',
            '凤', '麟', '龟', '蟹', '虾', '蚌', '螺', '蝶', '蜂', '蚁',
            '蜘', '蛛', '蚊', '蝇', '蛾', '萤', '蝉', '蟋', '蟀', '蚯',
            '蚓', '蜗', '牛', '青', '蛙', '蟾', '蜍', '蝌', '蚪', '鲤',
            '鲫', '鲨', '鲸', '海', '豚', '章', '鱼', '乌', '贼', '海',
            '星', '海', '马', '珊', '瑚', '贝', '壳', '珍', '珠', '玛',
            '瑙', '翡', '翠', '钻', '石', '宝', '玉', '琥', '珀', '水',
            '晶', '玻', '璃', '陶', '瓷', '砖', '瓦', '石', '头', '沙',
            '土', '泥', '尘', '灰', '烟', '雾', '气', '汽', '蒸', '汽'
        ]

        # 英文字母
        letters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'

        # 随机选择6个中文汉字
        chinese_part = ''.join(random.choices(chinese_chars, k=6))

        # 随机选择3个英文字母（随机大小写）
        english_part = ''.join(random.choices(letters, k=3))

        # 组合成最终用户名
        username = chinese_part + english_part

        return username

    def random_rename_step11(self, account_info, first_step_headers=None):
        """
        第十一步：随机修改用户名
        根据用户提供的API规范实现修改用户名接口
        """
        try:
            self.log_signal.emit("\n🎲 开始第十一步：随机修改用户名...")

            # 生成随机用户名
            new_username = self.generate_random_username()
            self.log_signal.emit(f"🎯 生成的随机用户名: {new_username}")

            # 从账号信息中提取必要的数据
            data_info = account_info.get('data', {})

            # 处理不同的数据结构
            if 'data' in data_info:
                # 注册流程的数据结构
                inner_data = data_info.get('data', {})
                token_info = inner_data.get('token_info', {})
                cookie_info = inner_data.get('cookie_info', {})
            else:
                # 登录流程的数据结构
                token_info = data_info.get('token_info', {})
                cookie_info = data_info.get('cookie_info', {})

            # 获取access_token
            access_token = token_info.get('access_token', '')
            if not access_token:
                self.log_signal.emit("❌ 缺少access_token，无法修改用户名")
                return {'success': False, 'error': '缺少access_token'}

            # 从cookies中获取mid
            mid = ""
            cookies_list = cookie_info.get('cookies', [])
            for cookie in cookies_list:
                if cookie.get('name') == 'DedeUserID':
                    mid = cookie.get('value', '')
                    break

            if not mid:
                self.log_signal.emit("❌ 缺少用户ID(mid)，无法修改用户名")
                return {'success': False, 'error': '缺少用户ID'}

            self.log_signal.emit(f"🔑 使用Access Token: {access_token[:30]}...")
            self.log_signal.emit(f"👤 用户ID(mid): {mid}")

            # 构建第十一步请求参数
            params = {
                "access_key": access_token,  # 这个就是账号的access_token值
                "appkey": "1d8b6e7d45233436",  # 固定
                "build": "8530200",  # 固定
                "c_locale": "zh-Hans_CN",  # 固定
                "channel": "oppo_tv.danmaku.bili_20200623",  # 固定
                "disable_rcmd": "0",  # 固定
                "mobi_app": "android",  # 固定
                "platform": "android",  # 固定
                "s_locale": "zh-Hans_CN",  # 固定
                "statistics": json.dumps({
                    "appId": 1,
                    "platform": 3,
                    "version": "8.53.0",
                    "abtest": ""
                }, separators=(',', ':')),  # 固定
                "ts": self.api_utils.get_current_timestamp(),  # 当前时间戳
                "uname": new_username  # 随机生成的用户名
            }

            # 生成签名
            params["sign"] = self.api_utils.generate_sign(params.copy())

            # 构建请求头 - 第11步需要特殊的authority
            headers = self.api_utils.build_headers_for_path(
                path="/x/member/app/uname/update",
                use_real_network_data=True,
                include_http2_pseudo_headers=False
            )

            # 添加/覆盖特定的请求头字段（按照用户提供的API规范）
            headers.update({
                "x-bili-mid": mid,  # mid在登录后的响应里面获取的
                "x-bili-metadata-legal-region": self.get_legal_region_from_country_code(self.country_code),  # 根据号码的归属地来的
                "x-bili-aurora-eid": self.api_utils.generate_aurora_eid(int(mid)) if mid.isdigit() else self.api_utils.generate_aurora_eid()  # 根据mid生成aurora-eid
            })

            # 如果提供了第一步的请求头，使用相同的x-bili-ticket
            if first_step_headers and 'x-bili-ticket' in first_step_headers:
                headers['x-bili-ticket'] = first_step_headers['x-bili-ticket']
                self.log_signal.emit("🔄 使用第一步相同的x-bili-ticket")
                self.log_signal.emit(f"🔄 第一步ticket: {first_step_headers['x-bili-ticket'][:50]}...")
                self.log_signal.emit(f"🔄 第十一步ticket: {headers['x-bili-ticket'][:50]}...")
            else:
                self.log_signal.emit("⚠️ 未提供第一步请求头，将使用新生成的x-bili-ticket")

            # 显示请求参数
            self.log_signal.emit("\n📋 第十一步请求参数:")
            for key, value in params.items():
                if key == 'access_key':
                    # access_key只显示前30个字符
                    display_value = str(value)[:30] + "..."
                else:
                    display_value = str(value)
                self.log_signal.emit(f"  {key}: {display_value}")

            # 构建请求体
            data = urlencode(params)
            content_length = str(len(data.encode('utf-8')))
            headers["content-length"] = content_length

            # 生成用于显示的完整请求头（按照真实请求头的顺序）
            display_headers = {
                ":method": "POST",
                ":authority": "api.bilibili.com",  # 第11步使用api.bilibili.com
                ":path": "/x/member/app/uname/update",
                ":scheme": "https",
                "accept": headers.get("accept", "*/*"),
                "accept-encoding": headers.get("accept-encoding", "gzip, deflate, br"),
                "app-key": headers.get("app-key", "android64"),
                "bili-http-engine": headers.get("bili-http-engine", "ignet"),
                "buvid": headers.get("buvid", ""),
                "content-length": content_length,
                "content-type": headers.get("content-type", "application/x-www-form-urlencoded; charset=utf-8"),
                "env": headers.get("env", "prod"),
                "fp_local": headers.get("fp_local", ""),
                "fp_remote": headers.get("fp_remote", ""),
                "guestid": headers.get("guestid", ""),
                "session_id": headers.get("session_id", ""),
                "user-agent": headers.get("user-agent", ""),
                "x-bili-aurora-eid": headers.get("x-bili-aurora-eid", ""),
                "x-bili-locale-bin": headers.get("x-bili-locale-bin", ""),
                "x-bili-metadata-ip-region": headers.get("x-bili-metadata-ip-region", "CN"),
                "x-bili-metadata-legal-region": headers.get("x-bili-metadata-legal-region", ""),
                "x-bili-mid": headers.get("x-bili-mid", ""),
                "x-bili-network-bin": headers.get("x-bili-network-bin", ""),
                "x-bili-ticket": headers.get("x-bili-ticket", ""),
                "x-bili-trace-id": headers.get("x-bili-trace-id", "")
            }

            # 显示完整的请求头详情
            self.log_signal.emit("\n📋 第十一步请求头详情:")
            for key, value in display_headers.items():
                if key == "x-bili-ticket" and value:
                    # x-bili-ticket只显示前50个字符
                    display_value = value[:50] + "..."
                elif key == "user-agent" and value and len(value) > 100:
                    # user-agent太长时截断显示
                    display_value = value[:100] + "..."
                else:
                    display_value = value
                self.log_signal.emit(f"  {key}: {display_value}")

            # 验证关键请求头字段
            self.log_signal.emit("\n🔍 关键请求头字段验证:")
            expected_headers = {
                "accept": "*/*",
                "accept-encoding": "gzip, deflate, br",
                "app-key": "android64",
                "bili-http-engine": "ignet",
                "content-type": "application/x-www-form-urlencoded; charset=utf-8",
                "env": "prod",
                "x-bili-metadata-ip-region": "CN"
            }

            for key, expected_value in expected_headers.items():
                actual_value = display_headers.get(key, "")
                if actual_value == expected_value:
                    self.log_signal.emit(f"  ✅ {key}: {actual_value}")
                else:
                    self.log_signal.emit(f"  ❌ {key}: 期望 '{expected_value}', 实际 '{actual_value}'")

            # 发送第十一步请求
            url = "https://api.bilibili.com/x/member/app/uname/update"
            self.log_signal.emit(f"\n🌐 发送第十一步修改用户名请求到: {url}")
            self.log_signal.emit(f"📦 请求体长度: {len(data)} 字节")

            # 使用TLS指纹会话发送请求
            tls_session = self.api_utils.get_tls_session()
            if tls_session:
                response = tls_session.post(
                    url=url,
                    data=data,
                    headers=headers,
                    timeout=30
                )
            else:
                # 备用方案：使用普通requests
                response = requests.post(
                    url=url,
                    data=data,
                    headers=headers,
                    timeout=30
                )

            # 解析第十一步响应
            self.log_signal.emit(f"📨 第十一步响应状态码: {response.status_code}")

            # 显示响应头
            self.log_signal.emit("📋 第十一步响应头:")
            for key, value in response.headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.log_signal.emit("✅ 第十一步响应解析成功")
                    self.log_signal.emit(f"📄 第十一步响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                    # 检查第十一步业务状态码
                    if result.get("code") == 0:
                        self.log_signal.emit("🎉 第十一步用户名修改成功！")
                        data = result.get("data", {})
                        final_name = data.get("name", new_username)
                        self.log_signal.emit(f"✨ 新用户名: {final_name}")

                        return {
                            'success': True,
                            'data': result,
                            'new_username': final_name,
                            'original_username': new_username
                        }
                    else:
                        error_msg = result.get('message', '未知错误')
                        self.log_signal.emit(f"❌ 第十一步业务错误: {error_msg}")
                        return {'success': False, 'error': error_msg, 'data': result}

                except json.JSONDecodeError as e:
                    self.log_signal.emit(f"❌ 第十一步响应JSON解析失败: {str(e)}")
                    self.log_signal.emit(f"📄 原始响应: {response.text}")
                    return {'success': False, 'error': 'JSON解析失败'}
            else:
                self.log_signal.emit(f"❌ 第十一步HTTP请求失败: {response.status_code}")
                self.log_signal.emit(f"📄 错误内容: {response.text}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}

        except requests.exceptions.Timeout:
            self.log_signal.emit("⏰ 第十一步请求超时")
            return {'success': False, 'error': '第十一步请求超时'}
        except requests.exceptions.ConnectionError:
            self.log_signal.emit("🌐 第十一步网络连接错误")
            return {'success': False, 'error': '第十一步网络连接错误'}
        except Exception as e:
            self.log_signal.emit(f"💥 第十一步未知错误: {str(e)}")
            return {'success': False, 'error': str(e)}

    def diagnose_network_issue(self):
        """诊断网络问题"""
        try:
            import socket

            # 测试DNS解析
            try:
                socket.gethostbyname("passport.bilibili.com")
                self.log_signal.emit("✅ DNS解析正常")
            except:
                self.log_signal.emit("❌ DNS解析失败")

            # 测试基本连接
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex(("passport.bilibili.com", 443))
                sock.close()
                if result == 0:
                    self.log_signal.emit("✅ 基本TCP连接正常")
                else:
                    self.log_signal.emit("❌ TCP连接失败")
            except:
                self.log_signal.emit("❌ TCP连接测试异常")

            # 检查代理设置
            import os
            proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
            proxy_found = False
            for var in proxy_vars:
                if os.environ.get(var):
                    self.log_signal.emit(f"🔍 检测到代理: {var}")
                    proxy_found = True

            if not proxy_found:
                self.log_signal.emit("✅ 未检测到代理设置")

            # 提供解决建议
            self.log_signal.emit("🔧 解决建议:")
            self.log_signal.emit("  1. 检查网络连接")
            self.log_signal.emit("  2. 检查防火墙设置")
            self.log_signal.emit("  3. 尝试禁用TLS指纹")
            self.log_signal.emit("  4. 检查代理设置")

        except Exception as e:
            self.log_signal.emit(f"⚠️ 网络诊断失败: {e}")

    def set_verification_code(self, code):
        """设置验证码"""
        self.verification_code = code

    def proceed_with_registration(self, verification_code, first_step_headers=None):
        """
        使用验证码进行注册或登录
        根据用户类型决定是注册流程还是登录流程
        """
        if not self.step2_result:
            self.log_signal.emit("❌ 缺少第二步结果，无法进行后续操作")
            return {'success': False, 'error': '缺少第二步结果'}

        # 检查用户类型
        step2_data = self.step2_result.get('data', {}).get('data', {})
        is_new_user = step2_data.get('is_new', True)

        if is_new_user:
            # 新用户：进行注册流程
            self.log_signal.emit(f"\n🔄 收到验证码: {verification_code}，开始第三步注册...")

            # 调用第三步注册
            step3_result = self.register_step3(self.step2_result, verification_code, first_step_headers)

            if step3_result.get('success'):
                self.log_signal.emit("✅ 第三步注册成功！")

                # 获取第三步返回的code值
                step3_data = step3_result.get('data', {}).get('data', {})
                registration_code = step3_data.get('code', '')

                if registration_code:
                    self.log_signal.emit(f"🔑 获得注册码: {registration_code}")
                    self.log_signal.emit("🚀 开始第四步：换取账号信息...")

                    # 调用第四步换取账号信息
                    step4_result = self.exchange_account_info(registration_code, first_step_headers)

                    if step4_result.get('success'):
                        self.emit_log("🎉 恭喜！b站用户注册流程全部完成！", is_important=True)
                        # 将第四步结果合并到返回数据中
                        step3_result['step4_data'] = step4_result.get('data', {})

                        # 如果勾选了自动答题，进行自动答题流程
                        if self.auto_answer:
                            self.emit_log("\n🤖 检测到自动答题已启用，开始自动答题流程...", is_important=True)
                            answer_result = self.auto_answer_questions(step4_result, first_step_headers)
                            if answer_result.get('success'):
                                step3_result['answer_data'] = answer_result.get('data', {})
                                self.emit_log("🎉 自动答题流程完成！", is_important=True)
                            else:
                                self.log_signal.emit(f"⚠️ 自动答题流程失败: {answer_result.get('error', '未知错误')}")
                                step3_result['answer_error'] = answer_result.get('error', '未知错误')

                        return step3_result
                    else:
                        self.emit_log(f"❌ 第四步换取账号信息失败: {step4_result.get('error', '未知错误')}", is_important=True)
                        # 即使第四步失败，第三步注册仍然成功
                        step3_result['step4_error'] = step4_result.get('error', '未知错误')
                        return step3_result
                else:
                    self.log_signal.emit("❌ 第三步响应中缺少注册码，无法进行第四步")
                    return step3_result
            else:
                self.emit_log(f"❌ 第三步注册失败: {step3_result.get('error', '未知错误')}", is_important=True)
                return step3_result
        else:
            # 老用户：进行登录流程
            self.log_signal.emit(f"\n🔄 收到验证码: {verification_code}，开始第五步登录...")

            # 调用第五步登录
            step5_result = self.login_step5(self.step2_result, verification_code, first_step_headers)

            if step5_result.get('success'):
                self.emit_log("🎉 恭喜！b站用户登录流程全部完成！", is_important=True)

                # 将第五步结果包装到step5_data中，以便保存账号记录时能正确识别
                wrapped_result = {
                    'success': True,
                    'step5_data': step5_result,  # 保存完整的step5结果
                    'data': step5_result.get('data', {}),  # 保持原有结构兼容性
                    'country_code': self.country_code,  # 添加区号信息
                    'phone_number': self.phone_number   # 添加手机号信息
                }

                # 第十步：检查老用户等级
                self.log_signal.emit("\n🔍 开始第十步：检查老用户等级...")
                step10_result = self.check_user_level_step10(wrapped_result, first_step_headers)

                if step10_result.get('success'):
                    # 将第十步结果添加到返回数据中
                    wrapped_result['step10_data'] = step10_result

                    # 检查是否需要继续后续流程
                    if step10_result.get('continue_flow', False):
                        user_level = step10_result.get('level', 0)
                        is_level_gte_zero = step10_result.get('is_level_gte_zero', False)

                        if is_level_gte_zero:
                            # 所有用户：根据等级和答题设置决定流程
                            self.log_signal.emit(f"✅ 用户等级为{user_level}级")

                            if user_level == 0:
                                # 0级用户：根据是否勾选答题决定流程
                                if self.auto_answer:
                                    # 0级用户勾选了答题：先保存基础账号记录，然后继续答题流程
                                    self.emit_log("🤖 0级用户检测到自动答题已启用，开始自动答题流程...", is_important=True)

                                    # 先保存基础账号记录
                                    self.log_signal.emit("💾 0级用户先保存基础账号记录，答题完成后更新记录类型")
                                    self.account_manager.save_account_record(
                                        country_code=self.country_code,
                                        phone_number=self.phone_number,
                                        access_token=step10_result.get('access_token', ''),
                                        refresh_token=step10_result.get('refresh_token', ''),
                                        cookies=step10_result.get('cookies', ''),
                                        record_type="单独登录"  # 基础记录类型，答题后会更新
                                    )

                                    # 标记已经保存过基础账号记录
                                    wrapped_result['account_already_saved'] = True

                                    # 继续答题流程
                                    answer_result = self.auto_answer_questions(step5_result, first_step_headers)
                                    if answer_result.get('success'):
                                        wrapped_result['answer_data'] = answer_result.get('data', {})
                                        self.emit_log("🎉 自动答题流程完成！", is_important=True)
                                    else:
                                        self.log_signal.emit(f"⚠️ 自动答题流程失败: {answer_result.get('error', '未知错误')}")
                                        wrapped_result['answer_error'] = answer_result.get('error', '未知错误')
                                else:
                                    # 0级用户未勾选答题：不保存到账号记录管理，直接结束
                                    self.log_signal.emit("❌ 0级用户未勾选自动答题，流程结束")
                                    self.log_signal.emit("ℹ️ 0级用户不保存到账号记录管理")
                                    self.log_signal.emit("🔄 流程结束")
                            elif user_level >= 1 and user_level <= 6:
                                # 1-6级用户：无论是否勾选答题，都直接保存账号记录
                                self.log_signal.emit(f"🔄 用户等级为{user_level}级(1-6级老用户)，直接保存账号记录后结束流程")
                                record_type = f"老用户{user_level}级"

                                # 保存账号记录
                                self.account_manager.save_account_record(
                                    country_code=self.country_code,
                                    phone_number=self.phone_number,
                                    access_token=step10_result.get('access_token', ''),
                                    refresh_token=step10_result.get('refresh_token', ''),
                                    cookies=step10_result.get('cookies', ''),
                                    record_type=record_type
                                )

                                self.log_signal.emit(f"💾 账号记录已保存，记录类型：{record_type}")
                                self.log_signal.emit(f"📱 区号：{self.country_code}")
                                self.log_signal.emit(f"📱 手机号：{self.phone_number}")
                                self.log_signal.emit(f"🔑 Access Token：{step10_result.get('access_token', '')[:30]}...")
                                self.log_signal.emit(f"🔑 Refresh Token：{step10_result.get('refresh_token', '')[:30]}...")
                                self.log_signal.emit(f"🍪 Cookies：{step10_result.get('cookies', '')[:50]}...")
                                self.log_signal.emit("🔄 流程结束")

                                # 标记已经保存过账号记录，避免主流程重复保存
                                wrapped_result['account_already_saved'] = True
                            else:
                                # >6级用户：等级过高，不保存到账号记录管理
                                self.log_signal.emit(f"ℹ️ 用户等级为{user_level}级(>6级)，等级过高，不保存到账号记录管理")
                                self.log_signal.emit("🔄 流程结束")
                    else:
                        self.log_signal.emit("🔄 用户等级>0级，已保存到账号记录管理，流程结束")
                        # 不进行自动答题，直接返回结果
                else:
                    self.log_signal.emit(f"⚠️ 第十步检查用户等级失败: {step10_result.get('error', '未知错误')}")
                    wrapped_result['step10_error'] = step10_result.get('error', '未知错误')

                    # 即使第十步失败，仍然可以进行自动答题（如果启用）
                    if self.auto_answer:
                        self.emit_log("\n🤖 检测到自动答题已启用，开始自动答题流程...", is_important=True)
                        answer_result = self.auto_answer_questions(step5_result, first_step_headers)
                        if answer_result.get('success'):
                            wrapped_result['answer_data'] = answer_result.get('data', {})
                            self.emit_log("🎉 自动答题流程完成！", is_important=True)
                        else:
                            self.log_signal.emit(f"⚠️ 自动答题流程失败: {answer_result.get('error', '未知错误')}")
                            wrapped_result['answer_error'] = answer_result.get('error', '未知错误')

                return wrapped_result
            else:
                self.emit_log(f"❌ 第五步登录失败: {step5_result.get('error', '未知错误')}", is_important=True)
                return step5_result

    def run(self):
        """执行短信发送"""
        try:
            self.emit_log("=== 开始生成设备信息 ===", is_important=True)
            self.progress_signal.emit(5)

            # 显示设备信息生成过程
            device_info = self.api_utils.get_device_info()
            self.emit_log(f"✓ 设备品牌: {device_info['brand']}")
            self.emit_log(f"✓ 设备型号: {device_info['model']}")
            self.emit_log(f"✓ Android版本: {device_info['android_version']}")
            self.emit_log(f"✓ IMEI: {device_info['imei']}")
            self.emit_log(f"✓ Android ID: {device_info['android_id']}")
            self.emit_log(f"✓ MAC地址: {device_info['mac_address']}")
            self.emit_log(f"✓ 硬件序列号: {device_info['serial_number']}")
            self.emit_log(f"✓ 硬件指纹: {device_info['device_fingerprint']}")
            self.emit_log(f"✓ CPU架构: {device_info['cpu_abi']}")
            self.emit_log(f"✓ 屏幕分辨率: {device_info['screen_resolution']}")
            self.emit_log(f"✓ 内存大小: {device_info['memory_total']}MB")

            # 显示设备唯一性保证
            stats = self.api_utils.get_device_statistics()
            self.log_signal.emit("\n=== 设备唯一性保证 ===")
            self.log_signal.emit("🔒 全球唯一性: 已确保")
            self.log_signal.emit("📊 数据库统计:")
            self.log_signal.emit(f"  总设备数: {stats['total_devices']} 个")
            if stats['first_generated']:
                self.log_signal.emit(f"  首次生成: {stats['first_generated']}")
                self.log_signal.emit(f"  最近生成: {stats['last_generated']}")
            self.log_signal.emit("🆔 当前设备唯一标识:")
            self.log_signal.emit(f"  硬件指纹: {device_info['device_fingerprint']}")
            import datetime
            self.log_signal.emit(f"  生成时间: {datetime.datetime.now().isoformat()}")
            self.log_signal.emit("✅ 此设备永不重复，全球唯一！")

            # 显示浏览器信息
            browser_info = device_info.get('browser_info', {})
            if browser_info:
                self.log_signal.emit("\n=== 浏览器信息 ===")
                self.log_signal.emit(f"✓ Chrome版本: {browser_info.get('chrome_version', 'N/A')}")
                self.log_signal.emit(f"✓ WebKit版本: {browser_info.get('webkit_version', 'N/A')}")
                self.log_signal.emit(f"✓ WebView版本: {browser_info.get('webview_version', 'N/A')}")
                self.log_signal.emit(f"✓ Safari版本: {browser_info.get('safari_version', 'N/A')}")
                self.log_signal.emit(f"✓ 构建版本: {browser_info.get('build_version', 'N/A')}")
                self.log_signal.emit(f"✓ SDK版本: {browser_info.get('sdk_int', 'N/A')}")

            # 显示TLS指纹详细信息
            self.log_signal.emit("\n=== TLS指纹信息 ===")
            detailed_tls_info = self.api_utils.get_detailed_tls_fingerprint_info()
            if detailed_tls_info:
                basic_info = detailed_tls_info['basic_info']
                self.log_signal.emit(f"✓ ja3指纹哈希: {basic_info['ja3_hash']}")
                self.log_signal.emit(f"✓ 设备tls指纹: {basic_info['device_tls_fingerprint']}")
                self.log_signal.emit(f"✓ tls版本: {basic_info['tls_version']}")

                # 显示密码套件信息
                cipher_info = detailed_tls_info['cipher_suites']
                self.log_signal.emit(f"✓ 密码套件数量: {cipher_info['count']}")
                self.log_signal.emit("✓ 密码套件列表:")
                for i, cipher in enumerate(cipher_info['list'][:5]):  # 只显示前5个
                    self.log_signal.emit(f"  {i+1}. {cipher}")
                if cipher_info['count'] > 5:
                    self.log_signal.emit(f"  ... 还有 {cipher_info['count'] - 5} 个")

                # 显示椭圆曲线信息
                curve_info = detailed_tls_info['supported_curves']
                self.log_signal.emit(f"✓ 椭圆曲线数量: {curve_info['count']}")
                self.log_signal.emit(f"✓ 椭圆曲线列表: {', '.join(curve_info['list'])}")

                # 显示签名算法信息
                sig_info = detailed_tls_info['signature_algorithms']
                self.log_signal.emit(f"✓ 签名算法数量: {sig_info['count']}")
                self.log_signal.emit("✓ 签名算法列表:")
                for i, sig_alg in enumerate(sig_info['list'][:3]):  # 只显示前3个
                    self.log_signal.emit(f"  {i+1}. {sig_alg}")
                if sig_info['count'] > 3:
                    self.log_signal.emit(f"  ... 还有 {sig_info['count'] - 3} 个")

                # 显示TLS扩展信息
                ext_info = detailed_tls_info['extensions']
                self.log_signal.emit(f"✓ tls扩展数量: {ext_info['count']}")
                self.log_signal.emit(f"✓ tls扩展列表: {', '.join(ext_info['list'][:8])}")  # 显示前8个
                if ext_info['count'] > 8:
                    self.log_signal.emit(f"  ... 还有 {ext_info['count'] - 8} 个")

                # 显示JA3字符串
                self.log_signal.emit(f"✓ ja3字符串: {basic_info['ja3_string']}")

                # 显示其他信息
                other_info = detailed_tls_info['other_info']
                self.log_signal.emit(f"✓ 会话id长度: {other_info['session_id_length']} 字节")
                self.log_signal.emit(f"✓ 随机数长度: {other_info['random_length']} 字节")
                self.log_signal.emit(f"✓ 压缩方法: {', '.join(other_info['compression_methods'])}")
            else:
                self.log_signal.emit("⚠️ tls指纹未生成或生成失败")

            self.progress_signal.emit(15)

            # 显示哔哩哔哩参数生成
            self.log_signal.emit("\n=== 哔哩哔哩参数生成 ===")
            self.log_signal.emit(f"✓ 生成BUVID: {self.api_utils.buvid}")
            self.log_signal.emit(f"✓ 生成设备游客ID: {self.api_utils.device_tourist_id}")
            self.log_signal.emit(f"✓ 生成本地ID: {self.api_utils.buvid}")
            self.log_signal.emit(f"✓ 生成登录会话ID: {self.api_utils.login_session_id}")
            self.log_signal.emit(f"✓ 生成链路追踪ID: {self.api_utils.generate_trace_id()}")
            self.log_signal.emit(f"✓ 时间戳: {self.api_utils.get_current_timestamp()}")
            self.progress_signal.emit(25)

            # 显示高级设备特征
            sensor_data = self.api_utils.get_sensor_data_snapshot()

            if sensor_data:
                self.log_signal.emit("\n=== 高级设备特征 ===")

                # 硬件认证证书
                if hasattr(self.api_utils, 'advanced_device_sim') and self.api_utils.advanced_device_sim:
                    attestation = self.api_utils.advanced_device_sim.generate_device_attestation()
                    self.log_signal.emit(f"🛡️ 硬件认证证书: {len(attestation)} 字符")

                # 传感器数据
                if sensor_data:
                    self.log_signal.emit("📱 传感器数据:")
                    if 'accelerometer' in sensor_data:
                        acc_values = [f"{v:.3f}" for v in sensor_data['accelerometer']['values']]
                        self.log_signal.emit(f"  加速度计: {acc_values}")
                    if 'gyroscope' in sensor_data:
                        gyro_values = [f"{v:.3f}" for v in sensor_data['gyroscope']['values']]
                        self.log_signal.emit(f"  陀螺仪: {gyro_values}")

                # 系统状态
                if hasattr(self.api_utils, 'advanced_device_sim') and self.api_utils.advanced_device_sim:
                    runtime_features = self.api_utils.advanced_device_sim.device_features.get('runtime_features', {})
                    if runtime_features:
                        self.log_signal.emit("💾 系统状态:")
                        self.log_signal.emit(f"  运行时间: {runtime_features.get('uptime', 0)} 秒")
                        memory_info = runtime_features.get('memory_info', {})
                        self.log_signal.emit(f"  内存使用: {memory_info.get('used_ram', 0)} MB")
                        battery_info = runtime_features.get('battery_info', {})
                        self.log_signal.emit(f"  电池电量: {battery_info.get('level', 0)}%")

                # 显示APK列表信息
                if hasattr(self.api_utils, 'advanced_device_sim') and self.api_utils.advanced_device_sim:
                    if hasattr(self.api_utils.advanced_device_sim, 'installed_apps'):
                        apps = self.api_utils.advanced_device_sim.installed_apps
                        system_apps = [app for app in apps if app.get('system', False)]
                        user_apps = [app for app in apps if not app.get('system', False)]
                        self.log_signal.emit(f"📦 已安装应用: {len(apps)} 个 (系统: {len(system_apps)}, 用户: {len(user_apps)})")

                    # 显示系统属性信息
                    if hasattr(self.api_utils.advanced_device_sim, 'system_properties'):
                        props = self.api_utils.advanced_device_sim.system_properties
                        self.log_signal.emit(f"⚙️ 系统属性: {len(props)} 个配置项")

                # 网络信息 - 使用与请求头编码相同的数据源
                network_info = self.api_utils.network_info
                if network_info:
                    self.log_signal.emit("🌐 网络信息 (自动检测):")
                    self.log_signal.emit(f"  运营商: {network_info.get('operator_name', 'Unknown')} ({network_info.get('operator_numeric', 'Unknown')})")
                    self.log_signal.emit(f"  网络类型: {network_info.get('network_type', 'Unknown')}")
                    signal_strength = network_info.get('signal_strength', {})
                    self.log_signal.emit(f"  信号强度: {signal_strength.get('dbm', 0)} dBm")
                    self.log_signal.emit("  ✅ 已根据当前IP自动配置运营商参数")



            self.progress_signal.emit(35)

            self.emit_log("\n🚀 开始获取验证码信息...", is_important=True)

            # 构建请求参数
            self.log_signal.emit("\n📝 构建请求参数...")
            params = self.api_utils.build_sms_send_params(self.phone_number, self.country_code)
            self.progress_signal.emit(45)

            # 显示完整的请求参数
            self.log_signal.emit("📋 请求参数详情:")
            for key, value in params.items():
                # 重要字段显示完整内容，其他字段适当截断
                important_fields = ['statistics', 'user-agent', 'sign', 'buvid', 'tel']
                if key in important_fields:
                    # 重要字段显示完整内容
                    display_value = str(value)
                elif len(str(value)) > 50:
                    # 其他长字段进行截断
                    display_value = str(value)[:47] + "..."
                else:
                    display_value = str(value)
                self.log_signal.emit(f"  {key}: {display_value}")

            # 构建请求头 - 使用真实网络数据以提高成功率
            self.log_signal.emit("\n🔧 构建请求头...")

            # 先生成用于实际请求的请求头（不包含HTTP/2伪头部）
            headers = self.api_utils.build_headers_for_path(
                path="/x/passport-login/sms/send",
                use_real_network_data=True,
                include_http2_pseudo_headers=False
            )

            # 先计算content-length
            form_data = urllib.parse.urlencode(params)
            content_length = str(len(form_data.encode('utf-8')))
            headers["content-length"] = content_length

            # 生成用于显示的完整请求头（按照真实请求头的顺序）
            # 按照用户提供的真实请求头顺序重新排列
            display_headers = {
                "accept": headers.get("accept", "*/*"),
                "accept-encoding": headers.get("accept-encoding", "gzip, deflate, br"),
                "app-key": headers.get("app-key", "android64"),
                "bili-http-engine": headers.get("bili-http-engine", "ignet"),
                "buvid": headers.get("buvid", ""),
                "content-type": headers.get("content-type", "application/x-www-form-urlencoded; charset=utf-8"),
                "env": headers.get("env", "prod"),
                "fp_local": headers.get("fp_local", ""),
                "fp_remote": headers.get("fp_remote", ""),
                "guestid": headers.get("guestid", ""),
                "session_id": headers.get("session_id", ""),
                "user-agent": headers.get("user-agent", ""),
                "x-bili-locale-bin": headers.get("x-bili-locale-bin", ""),
                "x-bili-metadata-ip-region": headers.get("x-bili-metadata-ip-region", "CN"),
                "x-bili-network-bin": headers.get("x-bili-network-bin", ""),
                "x-bili-ticket": headers.get("x-bili-ticket", ""),
                "x-bili-trace-id": headers.get("x-bili-trace-id", ""),
                ":method": "POST",
                ":authority": "passport.bilibili.com",
                ":path": "/x/passport-login/sms/send",
                ":scheme": "https",
                "content-length": content_length
            }
            self.progress_signal.emit(50)

            # 显示完整的请求头（包含伪头部用于调试）
            self.log_signal.emit("📋 请求头详情:")
            for key, value in display_headers.items():
                # 所有头部都显示完整内容
                display_value = str(value)
                self.log_signal.emit(f"  {key}: {display_value}")

            # 验证关键请求头字段
            self.log_signal.emit("\n🔍 关键请求头字段验证:")
            expected_headers = {
                "accept": "*/*",
                "accept-encoding": "gzip, deflate, br",
                "app-key": "android64",
                "bili-http-engine": "ignet",
                "content-type": "application/x-www-form-urlencoded; charset=utf-8",
                "env": "prod",
                "x-bili-metadata-ip-region": "CN"
            }

            for key, expected_value in expected_headers.items():
                actual_value = display_headers.get(key, "")
                if actual_value == expected_value:
                    self.log_signal.emit(f"  ✅ {key}: {actual_value}")
                else:
                    self.log_signal.emit(f"  ❌ {key}: 期望 '{expected_value}', 实际 '{actual_value}'")

            # 记录关键参数摘要
            self.log_signal.emit(f"\n📱 请求摘要:")
            self.log_signal.emit(f"  手机号: {self.phone_number}")
            self.log_signal.emit(f"  国家代码: {self.country_code}")
            self.log_signal.emit(f"  BUVID: {self.api_utils.buvid}")
            self.log_signal.emit(f"  API指纹: {self.api_utils.fp_local[:20]}...")
            self.log_signal.emit(f"  会话ID: {self.api_utils.session_id}")

            # 显示API签名生成详情
            sign = params.get('sign', '')
            self.log_signal.emit(f"\n🔐 API签名生成:")
            self.log_signal.emit(f"  使用APPKEY: {self.api_utils.APPKEY}")
            self.log_signal.emit(f"  使用APPSEC: {self.api_utils.APPSEC[:10]}...{self.api_utils.APPSEC[-10:]}")
            self.log_signal.emit(f"  生成签名: {sign}")

            # 显示内容长度信息
            self.log_signal.emit(f"  内容长度: {content_length} 字节")
            self.log_signal.emit(f"  参数总数: {len(params)} 个")
            self.progress_signal.emit(60)

            # 发送请求
            self.log_signal.emit("\n📡 发送验证码获取请求...")
            self.progress_signal.emit(70)

            url = "https://passport.bilibili.com/x/passport-login/sms/send"

            # 使用已经计算好的请求体
            data = form_data

            # 显示最终请求信息
            self.log_signal.emit(f"\n🌐 最终请求信息:")
            self.log_signal.emit(f"  请求URL: {url}")
            self.log_signal.emit(f"  请求方法: POST")
            self.log_signal.emit(f"  请求体长度: {len(data)} 字节")
            self.log_signal.emit(f"  请求头数量: {len(headers)} 个")
            self.log_signal.emit(f"  Content-Type: {headers.get('content-type', 'N/A')}")
            self.log_signal.emit(f"  User-Agent: {headers.get('user-agent', 'N/A')}")

            self.log_signal.emit(f"\n🚀 正在发送验证码获取请求...")

            # 显示代理信息
            if self.api_utils.is_proxy_enabled():
                proxy_info = self.api_utils.get_proxy_info()
                if proxy_info:
                    self.log_signal.emit(f"🌐 使用代理: {proxy_info['ip']}:{proxy_info['port']} ({proxy_info['protocol']})")

                    # 检查是否是会话锁定模式
                    if self.api_utils.proxy_manager and self.api_utils.proxy_manager.is_session_locked():
                        locked_proxy = self.api_utils.proxy_manager.get_locked_proxy()
                        if locked_proxy and locked_proxy['ip'] == proxy_info['ip'] and locked_proxy['port'] == proxy_info['port']:
                            self.log_signal.emit("🔒 会话锁定模式：复用已锁定的代理IP")
                        else:
                            self.log_signal.emit("⚠️ 会话锁定异常：当前代理与锁定代理不一致")
                    else:
                        self.log_signal.emit("🔄 动态代理模式：可能会切换IP")
                else:
                    self.log_signal.emit("🌐 代理已启用但未获取到代理信息")
            else:
                self.log_signal.emit("🌐 使用直连模式")

            # 发送HTTP请求 - 使用TLS指纹会话，支持代理重试
            max_retries = 3
            response = None

            for attempt in range(max_retries):
                try:
                    if attempt > 0:
                        self.log_signal.emit(f"🔄 第 {attempt + 1} 次尝试...")

                        # 如果启用了代理，尝试刷新代理
                        if self.api_utils.is_proxy_enabled():
                            # 检查是否启用了会话锁定
                            if self.api_utils.proxy_manager and self.api_utils.proxy_manager.is_session_locked():
                                locked_proxy = self.api_utils.proxy_manager.get_locked_proxy()
                                if locked_proxy:
                                    self.log_signal.emit("🔒 会话锁定模式，继续使用锁定代理")
                                else:
                                    self.log_signal.emit("🔓 锁定代理已失效，尝试获取新代理")
                                    if self.api_utils.refresh_proxy():
                                        proxy_info = self.api_utils.get_proxy_info()
                                        self.log_signal.emit(f"🔒 已获取新代理并重新锁定: {proxy_info['ip']}:{proxy_info['port']}")
                                    else:
                                        self.log_signal.emit("⚠️ 无法获取新代理")
                            else:
                                if self.api_utils.refresh_proxy():
                                    proxy_info = self.api_utils.get_proxy_info()
                                    self.log_signal.emit(f"🌐 已切换到新代理: {proxy_info['ip']}:{proxy_info['port']}")
                                else:
                                    self.log_signal.emit("⚠️ 无法获取新代理，继续使用当前配置")

                    tls_session = self.api_utils.get_tls_session()
                    if tls_session:
                        response = tls_session.post(
                            url=url,
                            data=data,
                            headers=headers,
                            timeout=30
                        )
                    else:
                        # 备用方案：使用普通requests
                        response = requests.post(
                            url=url,
                            data=data,
                            headers=headers,
                            timeout=30
                        )

                    # 如果请求成功，跳出重试循环
                    if response.status_code == 200:
                        break
                    else:
                        self.log_signal.emit(f"⚠️ 请求失败，状态码: {response.status_code}")
                        if attempt < max_retries - 1:
                            # 标记当前代理失效（如果使用代理）
                            if self.api_utils.is_proxy_enabled():
                                self.api_utils.mark_proxy_failed()
                            continue

                except requests.exceptions.RequestException as e:
                    self.log_signal.emit(f"⚠️ 网络请求异常: {str(e)}")
                    if attempt < max_retries - 1:
                        # 标记当前代理失效（如果使用代理）
                        if self.api_utils.is_proxy_enabled():
                            self.api_utils.mark_proxy_failed()
                        continue
                    else:
                        # 最后一次尝试失败，抛出异常
                        raise

            if response is None:
                raise Exception("所有重试尝试都失败了")

            self.progress_signal.emit(90)

            # 解析响应
            self.log_signal.emit(f"📨 响应状态码: {response.status_code}")

            # 显示响应头
            self.log_signal.emit("📋 响应头详情:")
            for key, value in response.headers.items():
                self.log_signal.emit(f"  {key}: {value}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.log_signal.emit("✅ 成功获取响应数据")
                    self.log_signal.emit(f"📄 响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                    # 检查业务状态码
                    if result.get("code") == 0:
                        data = result.get("data", {})

                        # 保存第一步的请求头，供第二步使用
                        result['first_step_headers'] = headers.copy()

                        # 检查是否需要验证码
                        if "recaptcha_url" in data and data["recaptcha_url"]:
                            self.emit_log("\n🔐 检测到极验3滑块验证码，开始自动处理...", is_important=True)
                            self.progress_signal.emit(75)

                            # 解析验证码参数
                            recaptcha_url = data["recaptcha_url"]
                            parsed_url = urlparse(recaptcha_url)
                            query_params = parse_qs(parsed_url.query)

                            gt = query_params.get('gee_gt', [None])[0]
                            challenge = query_params.get('gee_challenge', [None])[0]

                            if gt and challenge:
                                self.log_signal.emit(f"📋 验证码参数:")
                                self.log_signal.emit(f"  GT: {gt}")
                                self.log_signal.emit(f"  Challenge: {challenge}")

                                # 调用打码服务
                                captcha_result = self.solve_geetest_captcha(gt, challenge)

                                if captcha_result.get('success'):
                                    self.emit_log("✅ 极验3验证码自动处理完成！", is_important=True)
                                    self.progress_signal.emit(85)

                                    # 将验证码结果添加到返回数据中
                                    result['captcha_solved'] = True
                                    result['captcha_data'] = {
                                        'gt': gt,
                                        'challenge': challenge,  # 使用原始challenge（用于gee_challenge+5e）
                                        'original_challenge': challenge,  # 保存原始challenge
                                        'captcha_challenge': captcha_result['challenge'],  # 保存打码服务返回的challenge
                                        'validate': captcha_result['validate'],
                                        'seccode': captcha_result['seccode']
                                    }

                                    # 自动进行第二步SMS发送
                                    self.emit_log("\n🔄 验证码处理成功，自动进行第二步SMS发送...", is_important=True)
                                    self.progress_signal.emit(90)

                                    step2_result = self.send_sms_step2(result, result['captcha_data'], result.get('first_step_headers'))

                                    if step2_result.get('success'):
                                        # 检查用户类型，决定后续流程
                                        step2_data = step2_result.get('data', {}).get('data', {})
                                        is_new_user = step2_data.get('is_new', True)

                                        if is_new_user:
                                            # 新用户：继续注册流程
                                            self.emit_log("📱 SMS验证码发送成功，准备进行第三步注册...", is_important=True)
                                            result['step2_completed'] = True
                                            result['step2_data'] = step2_result.get('data', {})
                                            result['user_flow'] = 'registration'

                                            # 保存第二步结果，等待用户输入验证码
                                            # 将第一步的请求头也保存到第二步结果中
                                            step2_result['first_step_headers'] = result.get('first_step_headers')
                                            self.step2_result = step2_result

                                            # 如果启用了自动接码，尝试自动获取验证码
                                            if self.auto_sms_enabled:
                                                self.log_signal.emit("🤖 启用自动接码，开始获取验证码...")
                                                auto_code = self.fetch_sms_code()
                                                if auto_code:
                                                    self.log_signal.emit(f"✅ 自动获取验证码成功: {auto_code}")
                                                    # 发送自动获取验证码信号
                                                    self.auto_code_received_signal.emit(auto_code)
                                                    self.result_signal.emit(True, result)
                                                else:
                                                    self.emit_log("❌ 自动获取验证码失败，请手动输入", is_important=True)
                                                    # 发送信号通知GUI等待验证码输入
                                                    self.verification_code_signal.emit("自动获取验证码失败，请手动输入收到的短信验证码")
                                                    self.result_signal.emit(True, result)
                                            else:
                                                # 发送信号通知GUI等待验证码输入
                                                self.verification_code_signal.emit("请输入收到的短信验证码")
                                                self.result_signal.emit(True, result)
                                        else:
                                            # 老用户：直接进行第五步登录流程
                                            self.emit_log("📱 SMS验证码发送成功，准备进行第五步登录...", is_important=True)
                                            result['step2_completed'] = True
                                            result['step2_data'] = step2_result.get('data', {})
                                            result['user_flow'] = 'login'

                                            # 保存第二步结果，等待用户输入验证码
                                            # 将第一步的请求头也保存到第二步结果中
                                            step2_result['first_step_headers'] = result.get('first_step_headers')
                                            self.step2_result = step2_result

                                            # 如果启用了自动接码，尝试自动获取验证码
                                            if self.auto_sms_enabled:
                                                self.log_signal.emit("🤖 启用自动接码，开始获取验证码...")
                                                auto_code = self.fetch_sms_code()
                                                if auto_code:
                                                    self.log_signal.emit(f"✅ 自动获取验证码成功: {auto_code}")
                                                    # 发送自动获取验证码信号
                                                    self.auto_code_received_signal.emit(auto_code)
                                                    self.result_signal.emit(True, result)
                                                else:
                                                    self.emit_log("❌ 自动获取验证码失败，请手动输入", is_important=True)
                                                    # 发送信号通知GUI等待验证码输入
                                                    self.verification_code_signal.emit("自动获取验证码失败，请手动输入收到的短信验证码")
                                                    self.result_signal.emit(True, result)
                                            else:
                                                # 发送信号通知GUI等待验证码输入
                                                self.verification_code_signal.emit("请输入收到的短信验证码")
                                                self.result_signal.emit(True, result)
                                    else:
                                        self.log_signal.emit(f"❌ 第二步SMS发送失败: {step2_result.get('error', '未知错误')}")
                                        result['step2_completed'] = False
                                        result['step2_error'] = step2_result.get('error', '未知错误')
                                        self.result_signal.emit(False, result)
                                else:
                                    self.log_signal.emit(f"❌ 验证码处理失败: {captcha_result.get('error', '未知错误')}")
                                    result['captcha_solved'] = False
                                    result['captcha_error'] = captcha_result.get('error', '未知错误')
                                    self.result_signal.emit(False, result)
                            else:
                                self.log_signal.emit("❌ 无法解析验证码参数")
                                self.result_signal.emit(False, {"error": "无法解析验证码参数"})
                        else:
                            # 没有验证码，直接成功
                            self.log_signal.emit("✅ 无需验证码，直接成功！")
                            self.result_signal.emit(True, result)
                    else:
                        self.log_signal.emit(f"❌ 业务错误: {result.get('message', '未知错误')}")
                        self.result_signal.emit(False, result)

                except json.JSONDecodeError as e:
                    self.log_signal.emit(f"❌ JSON解析错误: {str(e)}")
                    self.log_signal.emit(f"📄 原始响应: {response.text}")
                    self.result_signal.emit(False, {"error": "JSON解析失败"})
            else:
                self.log_signal.emit(f"❌ HTTP请求失败: {response.status_code}")
                self.log_signal.emit(f"📄 错误内容: {response.text}")
                self.result_signal.emit(False, {"error": f"HTTP {response.status_code}"})

            self.progress_signal.emit(100)

        except requests.exceptions.Timeout:
            self.log_signal.emit("⏰ 请求超时")
            self.log_signal.emit("💡 建议: 检查网络连接或稍后重试")
            self.result_signal.emit(False, {"error": "请求超时"})
        except requests.exceptions.ConnectionError as e:
            self.log_signal.emit("🌐 网络连接错误")
            self.log_signal.emit(f"🔍 详细错误: {str(e)}")
            self.log_signal.emit("🔧 正在进行网络诊断...")

            # 进行简单的网络诊断
            self.diagnose_network_issue()

            self.result_signal.emit(False, {"error": "网络连接错误"})
        except requests.exceptions.SSLError as e:
            self.log_signal.emit("🔒 SSL/TLS连接错误")
            self.log_signal.emit(f"🔍 详细错误: {str(e)}")
            self.log_signal.emit("💡 建议: 可能是TLS指纹配置问题，尝试禁用TLS指纹")
            self.result_signal.emit(False, {"error": "SSL连接错误"})
        except Exception as e:
            self.log_signal.emit(f"💥 未知错误: {str(e)}")
            self.result_signal.emit(False, {"error": str(e)})


class BilibiliSMSGUI(QMainWindow):
    """主界面"""

    def __init__(self):
        super().__init__()
        self.worker = None
        self.registration_worker = None  # 注册工作线程
        self.proxy_test_worker = None    # 代理测试工作线程
        self.record_manager = AccountRecordManager()
        self.account_record_window = None

        # 线程管理器
        self.thread_manager = ThreadManager()

        # 先初始化UI
        self.init_ui()

        # 程序启动时不获取代理，创建无代理的API工具实例
        self.api_utils = BilibiliAPIUtils()
        print("🌐 程序启动完成，代理将在点击'获取验证码'时动态获取")

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        # 停止所有活跃线程
        active_count = self.thread_manager.get_active_count()
        if active_count > 0:
            self.add_log(f"🔄 正在停止 {active_count} 个活跃线程...")
            self.thread_manager.stop_all_threads()
            self.add_log("✅ 所有线程已停止")

        # 关闭账号记录窗口
        if self.account_record_window:
            self.account_record_window.close()

        event.accept()

    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口图标
        self.setWindowIcon(QIcon('Bilibili.png'))

        # 设置窗口标题
        self.setWindowTitle("b站协议注册")
        self.setGeometry(100, 100, 1000, 730)  # 横屏尺寸
        self.setMinimumSize(1000, 730)  # 横屏最小尺寸

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 改为水平布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(15)  # 设置布局间距
        main_layout.setContentsMargins(20, 20, 20, 20)  # 设置边距

        # 左侧导航区域
        nav_widget = QWidget()
        nav_widget.setFixedWidth(200)
        nav_layout = QVBoxLayout(nav_widget)
        nav_layout.setSpacing(5)
        nav_layout.setContentsMargins(0, 0, 0, 0)

        # 标题区域
        title_label = QLabel("🎯 B站协议注册工具")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setStyleSheet("color: #00A1D6; margin: 10px 0; padding: 10px; background-color: #F0F8FF; border-radius: 6px;")
        title_label.setAlignment(Qt.AlignCenter)
        nav_layout.addWidget(title_label)

        # 导航按钮
        self.nav_buttons = {}
        nav_items = [
            ("phone_config", "📱 手机号码配置"),
            ("captcha_config", "🔐 打码服务配置"),
            ("proxy_config", "🌐 代理服务配置")
        ]

        for key, text in nav_items:
            btn = QPushButton(text)
            btn.setFixedHeight(50)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #FFFFFF;
                    color: #595959;
                    border: 2px solid #E8E8E8;
                    border-radius: 8px;
                    font-weight: bold;
                    font-size: 11pt;
                    text-align: left;
                    padding: 12px 20px;
                    margin: 3px 0;
                }
                QPushButton:hover {
                    background-color: #F0F8FF;
                    border-color: #40A9FF;
                    color: #1890FF;
                }
                QPushButton:checked {
                    background-color: #1890FF;
                    color: white;
                    border-color: #1890FF;
                }
                QPushButton:pressed {
                    background-color: #096DD9;
                    border-color: #096DD9;
                }
            """)
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, k=key: self.switch_panel(k))
            self.nav_buttons[key] = btn
            nav_layout.addWidget(btn)

        nav_layout.addStretch()
        main_layout.addWidget(nav_widget)

        # 右侧内容区域
        self.content_widget = QWidget()
        content_layout = QVBoxLayout(self.content_widget)
        content_layout.setSpacing(10)
        content_layout.setContentsMargins(0, 0, 0, 0)

        # 创建各个面板
        self.create_panels()

        main_layout.addWidget(self.content_widget)

        # 默认选中第一个面板
        self.switch_panel("phone_config")

        # 状态栏
        self.statusBar().showMessage("🟢 系统就绪")
        self.statusBar().setStyleSheet("""
            QStatusBar {
                background-color: #F0F2F5;
                border-top: 1px solid #D9D9D9;
                font-weight: bold;
                color: #262626;
            }
        """)

        # 初始化日志
        self.add_log("🎯 B站协议注册工具已启动")
        self.add_log("💡 请在手机号码配置中填写：区号---手机号---接码地址")
        self.add_log("💡 新用户流程：获取验证码 → 输入短信验证码 → 完成注册 → 换取账号信息 → 自动答题（可选）")
        self.add_log("💡 老用户流程：获取验证码 → 输入短信验证码 → 完成登录 → 自动答题（可选）")

    def create_panels(self):
        """创建各个配置面板"""
        self.panels = {}

        # 手机号码配置面板
        self.panels["phone_config"] = self.create_phone_config_panel()

        # 打码服务配置面板
        self.panels["captcha_config"] = self.create_captcha_config_panel()

        # 代理服务配置面板
        self.panels["proxy_config"] = self.create_proxy_config_panel()

    def switch_panel(self, panel_key):
        """切换面板"""
        # 更新导航按钮状态
        for key, btn in self.nav_buttons.items():
            btn.setChecked(key == panel_key)

        # 清除当前内容
        layout = self.content_widget.layout()
        while layout.count():
            child = layout.takeAt(0)
            if child.widget():
                child.widget().setParent(None)

        # 显示选中的面板
        if panel_key in self.panels:
            layout.addWidget(self.panels[panel_key])

    def create_phone_config_panel(self):
        """创建手机号码配置面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        layout.setContentsMargins(0, 0, 0, 0)

        # 输入区域
        input_group = QGroupBox("📱 手机号码配置")
        input_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        input_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #E6F7FF;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #FAFCFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #1890FF;
                font-size: 11pt;
            }
        """)
        input_layout = QVBoxLayout(input_group)
        input_layout.setSpacing(12)
        input_layout.setContentsMargins(20, 25, 20, 15)

        # 整合的手机号码和接码地址输入
        phone_row = QHBoxLayout()
        phone_row.setSpacing(12)

        # 标签
        phone_label = QLabel("接码信息:")
        phone_label.setFont(QFont("Microsoft YaHei", 10))
        phone_label.setFixedWidth(80)
        phone_row.addWidget(phone_label)

        # 整合输入框
        self.phone_sms_input = QLineEdit()
        self.phone_sms_input.setPlaceholderText("格式: 区号---手机号---接码地址 (例: 852---44652292---http://217.194.135.59:30123/smsrcv.asp?mobile=85244652292&mtype=bilibili&pt=301250706165237&token=989b56661df06deec4a7870329cf0238)")
        self.phone_sms_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #D9D9D9;
                border-radius: 6px;
                padding: 10px 12px;
                font-size: 10pt;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #1890FF;
                background-color: #F0F8FF;
            }
        """)
        # 添加回车键支持
        self.phone_sms_input.returnPressed.connect(self.send_sms)
        phone_row.addWidget(self.phone_sms_input)

        # 发送按钮
        self.send_button = QPushButton("一键注册")
        self.send_button.clicked.connect(self.send_sms)
        self.send_button.setFixedSize(120, 40)
        self.send_button.setStyleSheet("""
            QPushButton {
                background-color: #1890FF;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #40A9FF;
            }
            QPushButton:pressed {
                background-color: #096DD9;
            }
            QPushButton:disabled {
                background-color: #D9D9D9;
                color: #BFBFBF;
            }
        """)
        phone_row.addWidget(self.send_button)

        input_layout.addLayout(phone_row)

        # 添加格式说明
        format_label = QLabel("💡 格式说明: 区号---手机号---接码地址")
        format_label.setFont(QFont("Microsoft YaHei", 9))
        format_label.setStyleSheet("color: #666666; margin-top: 8px;")
        input_layout.addWidget(format_label)

        # 第二行：自动答题选项
        auto_answer_row = QHBoxLayout()
        auto_answer_row.setSpacing(15)

        # 自动答题勾选框
        self.auto_answer_checkbox = QCheckBox("🤖 自动答题")
        self.auto_answer_checkbox.setFont(QFont("Microsoft YaHei", 10))
        self.auto_answer_checkbox.setStyleSheet("""
            QCheckBox {
                color: #1890FF;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #1890FF;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #1890FF;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QCheckBox::indicator:hover {
                border-color: #40A9FF;
            }
        """)
        auto_answer_row.addWidget(self.auto_answer_checkbox)

        # 答题间隔设置
        interval_label = QLabel("答题间隔:")
        interval_label.setFont(QFont("Microsoft YaHei", 9))
        interval_label.setStyleSheet("color: #595959;")
        auto_answer_row.addWidget(interval_label)

        self.answer_interval_input = QLineEdit()
        self.answer_interval_input.setPlaceholderText("秒")
        self.answer_interval_input.setText("3")  # 默认3秒
        self.answer_interval_input.setFixedWidth(60)
        self.answer_interval_input.setFont(QFont("Microsoft YaHei", 9))
        self.answer_interval_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #D9D9D9;
                border-radius: 4px;
                padding: 6px 10px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #1890FF;
            }
        """)
        auto_answer_row.addWidget(self.answer_interval_input)

        auto_answer_row.addStretch()
        input_layout.addLayout(auto_answer_row)

        # 第三行：随机改名选项
        auto_rename_row = QHBoxLayout()
        auto_rename_row.setSpacing(15)

        # 随机改名勾选框
        self.auto_rename_checkbox = QCheckBox("🎲 随机改名")
        self.auto_rename_checkbox.setFont(QFont("Microsoft YaHei", 10))
        self.auto_rename_checkbox.setStyleSheet("""
            QCheckBox {
                color: #52C41A;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #52C41A;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #52C41A;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QCheckBox::indicator:hover {
                border-color: #73D13D;
            }
        """)
        auto_rename_row.addWidget(self.auto_rename_checkbox)

        # 说明文字
        auto_desc = QLabel("勾选后将在注册/登录成功后自动进行答题(共60题)和随机改名")
        auto_desc.setFont(QFont("Microsoft YaHei", 9))
        auto_desc.setStyleSheet("color: #8C8C8C;")
        auto_rename_row.addWidget(auto_desc)

        auto_rename_row.addStretch()
        input_layout.addLayout(auto_rename_row)

        # 第四行：简化日志选项
        simple_log_row = QHBoxLayout()
        simple_log_row.setSpacing(15)

        # 简化日志勾选框
        self.simple_log_checkbox = QCheckBox("📝 简化日志")
        self.simple_log_checkbox.setChecked(False)  # 默认不启用简化日志
        self.simple_log_checkbox.setFont(QFont("Microsoft YaHei", 10))
        self.simple_log_checkbox.setStyleSheet("""
            QCheckBox {
                color: #FA8C16;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #FA8C16;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #FA8C16;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QCheckBox::indicator:hover {
                border-color: #FFA940;
            }
        """)
        simple_log_row.addWidget(self.simple_log_checkbox)

        # 说明文字
        simple_log_desc = QLabel("勾选后将只显示关键步骤日志，减少详细信息输出")
        simple_log_desc.setFont(QFont("Microsoft YaHei", 9))
        simple_log_desc.setStyleSheet("color: #8C8C8C;")
        simple_log_row.addWidget(simple_log_desc)

        simple_log_row.addStretch()
        input_layout.addLayout(simple_log_row)

        # 在手机号码配置GroupBox内添加账号记录按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()  # 推到右边

        self.account_record_button = QPushButton("📊 账号记录")
        self.account_record_button.clicked.connect(self.show_account_records)
        self.account_record_button.setFixedSize(120, 35)
        self.account_record_button.setStyleSheet("""
            QPushButton {
                background-color: #52C41A;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 10pt;
                margin-top: 10px;
            }
            QPushButton:hover {
                background-color: #73D13D;
            }
            QPushButton:pressed {
                background-color: #389E0D;
            }
        """)
        button_layout.addWidget(self.account_record_button)

        input_layout.addLayout(button_layout)

        layout.addWidget(input_group)

        # 添加日志区域到手机号码配置面板
        log_group = self.create_log_area()
        layout.addWidget(log_group)

        layout.addStretch()
        return panel



    def create_captcha_config_panel(self):
        """创建打码服务配置面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        layout.setContentsMargins(0, 0, 0, 0)

        # 验证码服务配置区域
        captcha_group = QGroupBox("🔐 打码服务配置")
        captcha_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        captcha_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #FFF2E8;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #FFFBF0;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #FA8C16;
                font-size: 11pt;
            }
        """)
        captcha_layout = QVBoxLayout(captcha_group)
        captcha_layout.setSpacing(12)
        captcha_layout.setContentsMargins(20, 25, 20, 15)

        # 服务地址输入行
        address_row = QHBoxLayout()
        address_row.setSpacing(12)

        captcha_label = QLabel("服务地址:")
        captcha_label.setFont(QFont("Microsoft YaHei", 10))
        captcha_label.setFixedWidth(80)
        address_row.addWidget(captcha_label)

        self.captcha_url_input = QLineEdit()
        self.captcha_url_input.setText("127.0.0.1:11667/kknsmdsjfkajy3")
        self.captcha_url_input.setPlaceholderText("例如: 127.0.0.1:11667/kknsmdsjfkajy3")
        self.captcha_url_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #D9D9D9;
                border-radius: 6px;
                padding: 10px 12px;
                font-size: 10pt;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #FA8C16;
                background-color: #FFF7E6;
            }
        """)
        address_row.addWidget(self.captcha_url_input)

        captcha_layout.addLayout(address_row)

        # 说明文字
        desc_label = QLabel("💡 配置打码服务地址，用于自动识别极验验证码")
        desc_label.setFont(QFont("Microsoft YaHei", 9))
        desc_label.setStyleSheet("color: #8C8C8C; margin-top: 8px;")
        captcha_layout.addWidget(desc_label)

        layout.addWidget(captcha_group)
        layout.addStretch()
        return panel

    def create_proxy_config_panel(self):
        """创建代理服务配置面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        layout.setContentsMargins(0, 0, 0, 0)

        # 代理配置区域
        proxy_group = QGroupBox("🌐 代理服务配置")
        proxy_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        proxy_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #F0F5FF;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #FAFCFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #722ED1;
                font-size: 11pt;
            }
        """)
        proxy_layout = QVBoxLayout(proxy_group)
        proxy_layout.setSpacing(12)
        proxy_layout.setContentsMargins(20, 25, 20, 15)

        # 第一行：启用代理选项
        proxy_enable_row = QHBoxLayout()
        proxy_enable_row.setSpacing(15)

        # 启用代理勾选框
        self.proxy_enabled_checkbox = QCheckBox("🔗 启用代理")
        self.proxy_enabled_checkbox.setChecked(False)  # 默认不启用代理
        self.proxy_enabled_checkbox.setFont(QFont("Microsoft YaHei", 10))
        self.proxy_enabled_checkbox.setStyleSheet("""
            QCheckBox {
                color: #722ED1;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #722ED1;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #722ED1;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QCheckBox::indicator:hover {
                border-color: #9254DE;
            }
        """)
        self.proxy_enabled_checkbox.stateChanged.connect(self.on_proxy_enabled_changed)
        proxy_enable_row.addWidget(self.proxy_enabled_checkbox)

        # 代理状态指示器
        self.proxy_status_label = QLabel("🔴 未启用")
        self.proxy_status_label.setFont(QFont("Microsoft YaHei", 9))
        self.proxy_status_label.setStyleSheet("color: #8C8C8C;")
        proxy_enable_row.addWidget(self.proxy_status_label)

        proxy_enable_row.addStretch()
        proxy_layout.addLayout(proxy_enable_row)

        # 第二行：代理API地址
        proxy_api_row = QHBoxLayout()
        proxy_api_row.setSpacing(12)

        proxy_api_label = QLabel("API地址:")
        proxy_api_label.setFont(QFont("Microsoft YaHei", 10))
        proxy_api_label.setFixedWidth(80)
        proxy_api_row.addWidget(proxy_api_label)

        self.proxy_api_input = QLineEdit()
        self.proxy_api_input.setText("https://ip.feihengip.com/servers.php?session=U26591e680718024212--8f8996bd8ebc700e58f571883202eeb4&time=1&count=1&type=text&pw=no&protocol=s5&iptype=tunnel&format=null&dev=web")
        self.proxy_api_input.setPlaceholderText("请输入代理API地址")
        self.proxy_api_input.setEnabled(False)  # 默认禁用
        self.proxy_api_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #D9D9D9;
                border-radius: 6px;
                padding: 10px 12px;
                font-size: 10pt;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #722ED1;
                background-color: #F9F0FF;
            }
            QLineEdit:disabled {
                background-color: #F5F5F5;
                color: #BFBFBF;
            }
        """)
        proxy_api_row.addWidget(self.proxy_api_input)

        # 测试代理按钮
        self.test_proxy_button = QPushButton("测试")
        self.test_proxy_button.clicked.connect(self.test_proxy_connection)
        self.test_proxy_button.setFixedSize(80, 40)
        self.test_proxy_button.setEnabled(False)  # 默认禁用
        self.test_proxy_button.setStyleSheet("""
            QPushButton {
                background-color: #722ED1;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #9254DE;
            }
            QPushButton:pressed {
                background-color: #531DAB;
            }
            QPushButton:disabled {
                background-color: #D9D9D9;
                color: #BFBFBF;
            }
        """)
        proxy_api_row.addWidget(self.test_proxy_button)

        proxy_layout.addLayout(proxy_api_row)

        # 第三行：说明文字
        proxy_desc_row = QHBoxLayout()
        proxy_desc = QLabel("💡 每次点击'获取验证码'会获取新代理IP，当前会话全程使用该IP")
        proxy_desc.setFont(QFont("Microsoft YaHei", 9))
        proxy_desc.setStyleSheet("color: #8C8C8C; margin-top: 8px;")
        proxy_desc_row.addWidget(proxy_desc)
        proxy_desc_row.addStretch()
        proxy_layout.addLayout(proxy_desc_row)

        layout.addWidget(proxy_group)
        layout.addStretch()
        return panel

    def create_log_area(self):
        """创建日志区域"""
        # 日志区域
        log_group = QGroupBox("📋 运行日志")
        log_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        log_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #F0F0F0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #FAFAFA;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #595959;
                font-size: 11pt;
            }
        """)
        log_layout = QVBoxLayout(log_group)
        log_layout.setContentsMargins(15, 25, 15, 15)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #E8E8E8;
                border-radius: 6px;
                background-color: #FFFFFF;
                padding: 10px;
                line-height: 1.4;
            }
        """)
        log_layout.addWidget(self.log_text)

        # 按钮区域
        button_layout = QHBoxLayout()



        # 清空日志按钮
        clear_button = QPushButton("清空日志")
        clear_button.clicked.connect(self.clear_log)
        clear_button.setFixedSize(100, 30)
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #FF4D4F;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #FF7875;
            }
            QPushButton:pressed {
                background-color: #D9363E;
            }
        """)

        # 按钮居右对齐
        button_layout.addStretch()
        button_layout.addWidget(clear_button)
        log_layout.addLayout(button_layout)

        return log_group



    def add_log(self, message):
        """添加日志"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_text.append(log_message)

        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def add_simple_log(self, message):
        """添加简化日志信息（用于简化模式）"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_text.append(log_message)

        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.add_log("📝 日志已清空")



        # 设置焦点到手机号输入框
        self.phone_sms_input.setFocus()

        # 添加日志
        self.add_log("🔄 流程已重置，可以重新开始注册")
        self.add_log("📖 请输入接码信息并点击获取验证码开始使用")



    def show_account_records(self):
        """显示账号记录窗口"""
        if self.account_record_window is None:
            self.account_record_window = AccountRecordWindow(self)

        self.account_record_window.show()
        self.account_record_window.raise_()
        self.account_record_window.activateWindow()

    def save_account_record(self, result):
        """保存账号记录"""
        try:
            # 获取基本信息
            country_code = self.country_code if hasattr(self, 'country_code') else ""
            phone_number = self.phone_number if hasattr(self, 'phone_number') else ""

            # 从结果中提取token和cookie信息
            access_token = ""
            refresh_token = ""
            cookies = ""
            record_type = "单独注册"  # 默认记录类型

            # 首先检查是否为高危账号
            high_risk_detected = False

            # 检查不同步骤的结果数据中是否有高危标识
            data = None
            if 'step4_data' in result:
                # 第四步（注册后换取账号信息）的数据
                data = result['step4_data'].get('data', {})
                # 检查高危状态
                if data.get('status') == 2 and '高危异常行为' in str(data.get('message', '')):
                    high_risk_detected = True
                    record_type = "高危"
                elif 'answer_data' in result:
                    # 检查是否有随机改名结果
                    answer_data = result['answer_data']
                    if answer_data.get('rename_result', {}).get('success'):
                        record_type = "注册成功并答题2级"  # 包含随机改名
                    else:
                        record_type = "注册成功并答题"
                else:
                    record_type = "单独注册"
            elif 'step5_data' in result:
                # 第五步（老用户登录）的数据
                step5_data = result['step5_data']
                # 从step5_data中提取实际的data
                data = step5_data.get('data', {}) if isinstance(step5_data, dict) else {}

                # 检查高危状态
                if data.get('status') == 2 and '高危异常行为' in str(data.get('message', '')):
                    high_risk_detected = True
                    record_type = "高危"
                elif 'answer_data' in result:
                    # 检查是否有随机改名结果
                    answer_data = result['answer_data']
                    if answer_data.get('rename_result', {}).get('success'):
                        record_type = "登录成功并答题2级"  # 包含随机改名
                    else:
                        record_type = "登录成功并答题"
                else:
                    record_type = "单独登录"
            elif 'data' in result:
                # 直接的数据
                data = result['data']
                # 检查高危状态
                if data.get('status') == 2 and '高危异常行为' in str(data.get('message', '')):
                    high_risk_detected = True
                    record_type = "高危"
                elif 'answer_data' in result:
                    # 如果有答题数据但没有明确的步骤数据，可能是仅答题的情况
                    record_type = "仅答题成功"
                else:
                    record_type = "单独注册"

            # 如果检测到高危账号，直接保存高危记录
            if high_risk_detected:
                # 提取高危信息
                high_risk_message = data.get('message', '账号存在高危异常行为')
                high_risk_url = data.get('url', '')

                # 保存高危账号记录（token和cookies为空）
                self.record_manager.save_account_record(
                    country_code, phone_number, "", "", "", record_type
                )
                self.add_log("⚠️ 检测到高危账号，已记录")
                self.add_log(f"🚨 高危信息: {high_risk_message}")
                if high_risk_url:
                    self.add_log(f"🔗 处理链接: {high_risk_url}")
                return

            if data:
                # 提取token信息
                token_info = data.get("token_info", {})
                if token_info:
                    access_token = token_info.get("access_token", "")
                    refresh_token = token_info.get("refresh_token", "")

                # 提取cookie信息
                cookie_info = data.get("cookie_info", {})
                if cookie_info:
                    cookies_list = cookie_info.get("cookies", [])
                    # 将所有cookies连在一起，用分号分隔
                    cookie_parts = []
                    for cookie in cookies_list:
                        cookie_name = cookie.get("name", "")
                        cookie_value = cookie.get("value", "")
                        if cookie_name and cookie_value:
                            cookie_parts.append(f"{cookie_name}={cookie_value}")
                    cookies = "; ".join(cookie_parts)

            # 检查是否有答题完成但没有token的情况（仅答题成功）
            if not (access_token or refresh_token or cookies) and 'answer_data' in result:
                # 如果有答题数据但没有token，仍然保存记录，标记为仅答题
                answer_data = result['answer_data']
                if answer_data.get('submit_result', {}).get('success'):
                    record_type = "仅答题成功"
                    # 保存一个空的记录，但标记答题成功
                    self.record_manager.save_account_record(
                        country_code, phone_number, "", "", "", record_type
                    )
                    self.add_log("💾 答题记录已保存")
                    return

            # 只有在有有效数据时才保存
            if access_token or refresh_token or cookies:
                self.record_manager.save_account_record(
                    country_code, phone_number, access_token, refresh_token, cookies, record_type
                )
                self.add_log("💾 账号记录已保存")
                self.add_log(f"🔑 Access Token: {access_token[:30]}..." if access_token else "🔑 Access Token: 空")
                self.add_log(f"🔑 Refresh Token: {refresh_token[:30]}..." if refresh_token else "🔑 Refresh Token: 空")
                self.add_log(f"🍪 Cookies: {cookies[:50]}..." if cookies else "🍪 Cookies: 空")
            else:
                self.add_log("⚠️ 未找到有效的账号信息，跳过保存")

        except Exception as e:
            self.add_log(f"❌ 保存账号记录失败: {str(e)}")

    def get_country_code(self):
        """获取选中的国家代码"""
        return self.country_code if hasattr(self, 'country_code') else ""



    def send_sms(self):
        """发送短信验证码"""
        phone_sms_info = self.phone_sms_input.text().strip()

        if not phone_sms_info:
            QMessageBox.warning(self, "警告", "请输入接码信息！")
            self.phone_sms_input.setFocus()
            return

        # 解析输入格式: 区号---手机号---接码地址
        parts = phone_sms_info.split('---')
        if len(parts) != 3:
            QMessageBox.warning(self, "警告", "格式错误！请按照格式输入：区号---手机号---接码地址")
            self.phone_sms_input.setFocus()
            return

        country_code, phone, sms_url = parts
        country_code = country_code.strip()
        phone = phone.strip()
        sms_url = sms_url.strip()

        if not country_code or not phone or not sms_url:
            QMessageBox.warning(self, "警告", "区号、手机号和接码地址都不能为空！")
            self.phone_sms_input.setFocus()
            return

        if not country_code.isdigit():
            QMessageBox.warning(self, "警告", "区号只能包含数字！")
            self.phone_sms_input.setFocus()
            return

        if not phone.isdigit():
            QMessageBox.warning(self, "警告", "手机号码只能包含数字！")
            self.phone_sms_input.setFocus()
            return

        if not sms_url.startswith(('http://', 'https://')):
            QMessageBox.warning(self, "警告", "接码地址必须是有效的URL！")
            self.phone_sms_input.setFocus()
            return

        # 保存接码地址供后续使用
        self.sms_receive_url = sms_url
        self.country_code = country_code
        self.phone_number = phone

        # 禁用发送按钮和输入框
        self.send_button.setEnabled(False)
        self.send_button.setText("注册中...")
        self.phone_sms_input.setEnabled(False)




        self.statusBar().showMessage("🚀 正在获取验证码信息...")

        # 检查是否已有SMS工作线程在运行
        if self.worker and self.worker.isRunning():
            self.add_log("⚠️ 已有注册流程正在进行中，请稍候...")
            # 恢复UI状态
            self.send_button.setEnabled(True)
            self.send_button.setText("一键注册")
            self.phone_sms_input.setEnabled(True)
            return

        # 创建工作线程
        captcha_url = self.captcha_url_input.text().strip() or "127.0.0.1:11667/kknsmdsjfkajy3"
        auto_answer = self.auto_answer_checkbox.isChecked()

        # 获取答题间隔
        try:
            answer_interval = int(self.answer_interval_input.text().strip() or "3")
            if answer_interval < 1:
                answer_interval = 3
        except ValueError:
            answer_interval = 3

        # 获取随机改名设置
        auto_rename = self.auto_rename_checkbox.isChecked()

        # 获取简化日志设置
        simple_log = self.simple_log_checkbox.isChecked()

        # 获取代理配置，每次都创建新的API工具实例以获取新代理
        proxy_config = self.get_proxy_config()

        if proxy_config and proxy_config.get('enabled'):
            # 每次点击都创建新的API工具实例，获取新的代理IP
            new_api_utils = BilibiliAPIUtils(proxy_config=proxy_config)
            proxy_info = new_api_utils.get_proxy_info()
            if proxy_info:
                self.add_log(f"🆕 已获取新代理: {proxy_info['ip']}:{proxy_info['port']}")
                self.add_log("🔒 当前会话将全程使用此代理IP")
            else:
                self.add_log("⚠️ 代理获取失败，使用直连模式")
                new_api_utils = BilibiliAPIUtils()

            # 更新主界面的API工具实例，确保后续操作使用相同代理
            self.api_utils = new_api_utils
            self.worker = SMSWorker(phone, country_code, captcha_url, auto_answer, answer_interval, auto_rename, new_api_utils, self.sms_receive_url, simple_log)
        else:
            # 未启用代理，使用直连
            self.add_log("🌐 使用直连模式")
            self.worker = SMSWorker(phone, country_code, captcha_url, auto_answer, answer_interval, auto_rename, self.api_utils, self.sms_receive_url, simple_log)

        # 连接信号
        self.worker.log_signal.connect(self.add_log)
        self.worker.result_signal.connect(self.on_result)
        self.worker.verification_code_signal.connect(self.on_verification_code_needed)
        self.worker.auto_code_received_signal.connect(self.on_auto_code_received)

        # 添加到线程管理器
        self.thread_manager.add_thread(self.worker)

        # 启动线程
        self.worker.start()

    def on_result(self, success, result):
        """处理发送结果"""
        # 恢复UI状态
        self.send_button.setEnabled(True)
        self.send_button.setText("一键注册")
        self.phone_sms_input.setEnabled(True)


        if success:
            # 检查是否完成了第二步
            if result.get('step2_completed'):
                self.statusBar().showMessage("🎉 SMS验证码发送成功！请查收短信")

            elif result.get('step2_completed') is False:
                # 第二步失败
                self.statusBar().showMessage("❌ 第二步SMS发送失败")
                error_msg = result.get('step2_error', '第二步SMS发送失败')
                self.add_log(f"❌ 第二步失败: {error_msg}")

            elif result.get('captcha_solved'):
                # 只完成了验证码，但没有第二步信息（不应该发生）
                self.statusBar().showMessage("🎉 极验3验证码自动处理成功！")
            else:
                # 检查是否获取到验证码URL但未处理
                data = result.get("data", {}) if result else {}
                if "recaptcha_url" in data and data["recaptcha_url"]:
                    if result.get('captcha_solved') is False:
                        self.statusBar().showMessage("❌ 验证码处理失败")
                        error_msg = result.get('captcha_error', '验证码处理失败')
                        self.add_log(f"❌ 验证码处理失败: {error_msg}")
                    else:
                        self.statusBar().showMessage("✅ 成功获取极验3滑块验证码！")
                else:
                    self.statusBar().showMessage("✅ 验证码获取成功！")
        else:
            self.statusBar().showMessage("❌ 操作失败")

            # 显示错误信息
            error_msg = result.get("message", result.get("error", "未知错误"))
            self.add_log(f"❌ 操作失败: {error_msg}")

    def on_verification_code_needed(self, message):
        """处理需要验证码输入的信号 - 现在自动获取验证码失败时显示提示"""
        self.add_log(f"📱 {message}")
        self.add_log("⚠️ 自动获取验证码失败，请检查接码地址是否正确")


        self.statusBar().showMessage("❌ 自动获取验证码失败")

    def on_auto_code_received(self, verification_code):
        """处理自动获取到的验证码 - 直接自动完成注册/登录"""
        self.add_log(f"🤖 自动获取验证码成功: {verification_code}")


        self.statusBar().showMessage("🤖 验证码已自动获取，正在完成注册/登录...")

        # 直接调用完成注册/登录，传入验证码
        self.complete_registration_with_code(verification_code)

    def complete_registration_with_code(self, verification_code):
        """使用指定验证码完成注册或登录"""
        if not verification_code:
            self.add_log("❌ 验证码为空")
            return

        if not verification_code.isdigit() or len(verification_code) != 6:
            self.add_log("❌ 验证码格式错误，应为6位数字")
            return

        if not self.worker or not self.worker.step2_result:
            self.add_log("❌ 缺少前置步骤结果，请重新获取验证码")
            return

        # 检查用户类型
        step2_data = self.worker.step2_result.get('data', {}).get('data', {})
        is_new_user = step2_data.get('is_new', True)

        # 禁用按钮和输入框
        self.phone_sms_input.setEnabled(False)
        self.send_button.setEnabled(False)

        if is_new_user:
            self.send_button.setText("注册中...")
            status_message = "🚀 正在进行用户注册..."
            log_message = f"🚀 开始使用验证码 {verification_code} 进行注册..."
            success_message = "🎉 用户注册成功！"
            fail_message = "❌ 注册失败"
            error_prefix = "注册失败"
            exception_message = "💥 注册过程发生异常"
            exception_status = "❌ 注册异常"
        else:
            self.send_button.setText("登录中...")
            status_message = "🚀 正在进行用户登录..."
            log_message = f"🚀 开始使用验证码 {verification_code} 进行登录..."
            success_message = "🎉 用户登录成功！"
            fail_message = "❌ 登录失败"
            error_prefix = "登录失败"
            exception_message = "💥 登录过程发生异常"
            exception_status = "❌ 登录异常"




        self.statusBar().showMessage(status_message)

        self.add_log(log_message)

        # 获取第一步的请求头（如果存在）
        first_step_headers = None
        if hasattr(self.worker, 'step2_result') and self.worker.step2_result:
            # 从第二步结果中获取第一步的请求头
            if 'first_step_headers' in self.worker.step2_result:
                first_step_headers = self.worker.step2_result['first_step_headers']
                self.add_log("🔄 成功获取第一步请求头，将在后续步骤中保持x-bili-ticket一致性")
                if first_step_headers and 'x-bili-ticket' in first_step_headers:
                    self.add_log(f"🔄 第一步x-bili-ticket: {first_step_headers['x-bili-ticket'][:50]}...")
            else:
                self.add_log("⚠️ 未找到第一步请求头，后续步骤将使用新生成的x-bili-ticket")

        # 检查是否已有注册工作线程在运行
        if self.registration_worker and self.registration_worker.isRunning():
            self.add_log("⚠️ 已有注册/登录流程正在进行中，请稍候...")
            # 恢复UI状态
            self.phone_sms_input.setEnabled(True)
            self.send_button.setEnabled(True)
            self.send_button.setText("一键注册")
            return

        # 创建新的工作线程进行注册或登录
        try:
            # 创建注册工作线程
            self.registration_worker = RegistrationWorker(
                self.worker, verification_code, first_step_headers
            )

            # 连接信号
            self.registration_worker.log_signal.connect(self.add_log)
            self.registration_worker.finished_signal.connect(
                lambda result: self.on_registration_finished(
                    result, is_new_user, success_message, fail_message,
                    error_prefix, exception_message, exception_status
                )
            )

            # 添加到线程管理器
            self.thread_manager.add_thread(self.registration_worker)

            # 启动线程
            self.registration_worker.start()

        except Exception as e:
            self.add_log(f"{exception_message}: {str(e)}")
            self.statusBar().showMessage(exception_status)

            # 恢复UI状态
            self.phone_sms_input.setEnabled(True)
            self.send_button.setEnabled(True)
            self.send_button.setText("一键注册")


    def complete_registration(self):
        """完成注册或登录（已废弃，因为移除了验证码输入界面）"""
        self.add_log("⚠️ 此方法已废弃，请使用自动验证码功能")
        return

    def on_registration_finished(self, result, is_new_user, success_message, fail_message,
                                error_prefix, exception_message, exception_status):
        """处理注册/登录完成的结果"""
        try:
            # 恢复UI状态

            if result.get('success'):
                self.statusBar().showMessage(success_message)

                # 保存账号记录
                account_already_saved = result.get('account_already_saved', False)

                if not account_already_saved:
                    # 第十步没有保存过，正常保存
                    # 检查是否勾选了随机改名且改名失败
                    should_save = True
                    if 'answer_data' in result:
                        answer_data = result['answer_data']
                        # 如果有答题数据，检查是否勾选了随机改名
                        if self.auto_rename_checkbox.isChecked():
                            # 勾选了随机改名，检查改名是否成功
                            rename_result = answer_data.get('rename_result')
                            if rename_result is None:
                                # 没有改名结果，说明改名没有执行或失败
                                should_save = False
                                self.add_log("⚠️ 勾选了随机改名但改名未执行，不保存账号记录")
                            elif not rename_result.get('success', False):
                                # 改名失败
                                should_save = False
                                self.add_log("⚠️ 随机改名失败，不保存账号记录")
                            else:
                                # 改名成功
                                self.add_log("✅ 随机改名成功，保存账号记录")

                    if should_save:
                        self.save_account_record(result)
                elif 'answer_data' in result:
                    # 第十步已经保存过，但有答题数据，需要更新记录类型
                    self.add_log("🔄 检测到答题数据，更新账号记录类型...")
                    answer_data = result['answer_data']

                    # 检查是否勾选了随机改名且改名成功
                    should_update = True
                    new_record_type = "登录成功并答题"

                    if self.auto_rename_checkbox.isChecked():
                        # 勾选了随机改名，检查改名是否成功
                        rename_result = answer_data.get('rename_result')
                        if rename_result and rename_result.get('success', False):
                            # 改名成功
                            new_record_type = "登录成功并答题2级"
                            self.add_log("✅ 随机改名成功，更新为2级记录")
                        elif rename_result is None:
                            # 没有改名结果，说明改名没有执行或失败
                            should_update = False
                            self.add_log("⚠️ 勾选了随机改名但改名未执行，不更新记录类型")
                        elif not rename_result.get('success', False):
                            # 改名失败
                            should_update = False
                            self.add_log("⚠️ 随机改名失败，不更新记录类型")

                    if should_update:
                        # 更新记录类型
                        country_code = result.get('country_code', self.get_country_code())
                        phone_number = result.get('phone_number', self.phone_number if hasattr(self, 'phone_number') else "")
                        self.record_manager.update_record_type(country_code, phone_number, new_record_type)
                        self.add_log(f"💾 账号记录类型已更新为: {new_record_type}")
                    else:
                        self.add_log("ℹ️ 跳过保存账号记录")
                else:
                    self.add_log("ℹ️ 账号记录已在第十步中保存，跳过重复保存")

                # 对于注册流程，检查第四步是否有错误
                if is_new_user and 'step4_error' in result:
                    self.add_log(f"⚠️ 第四步失败: {result['step4_error']}")
                    self.add_log("ℹ️ 注册成功但账号信息换取失败，可手动使用注册码换取")

                # 恢复输入控件，允许直接进行下一次注册
                self.phone_sms_input.setEnabled(True)
                self.send_button.setEnabled(True)
                self.send_button.setText("一键注册")

                # 显示完成提示
                self.add_log("✅ 流程已完成！可以直接进行下一次注册")

            else:
                # 检查是否为高危账号
                if result.get('high_risk'):
                    self.statusBar().showMessage("🚨 检测到高危账号")

                    # 保存高危账号记录
                    self.save_account_record(result)

                    # 恢复输入控件，允许重试
                    self.phone_sms_input.setEnabled(True)
                    self.send_button.setEnabled(True)
                    self.send_button.setText("一键注册")

                    self.add_log("🚨 高危账号已记录，请处理高危问题后重试")
                else:
                    self.statusBar().showMessage(fail_message)
                    error_msg = result.get('error', error_prefix)
                    self.add_log(f"❌ {error_prefix}: {error_msg}")

                    # 重新启用相关控件
                    self.phone_sms_input.setEnabled(True)
                    self.send_button.setEnabled(True)
                    self.send_button.setText("一键注册")

        except Exception as e:
            self.add_log(f"{exception_message}: {str(e)}")
            self.statusBar().showMessage(exception_status)

            # 恢复UI状态
            self.phone_sms_input.setEnabled(True)
            self.send_button.setEnabled(True)
            self.send_button.setText("一键注册")

    def on_proxy_enabled_changed(self, state):
        """代理启用状态改变时的处理"""
        enabled = state == 2  # Qt.Checked

        # 启用/禁用相关控件
        self.proxy_api_input.setEnabled(enabled)
        self.test_proxy_button.setEnabled(enabled)

        # 更新状态指示器
        if enabled:
            self.proxy_status_label.setText("🟡 已启用")
            self.proxy_status_label.setStyleSheet("color: #FA8C16;")
            self.add_log("🌐 代理功能已启用")
            self.add_log("⚠️ 代理配置已更改，请重启程序以应用新配置")
        else:
            self.proxy_status_label.setText("🔴 未启用")
            self.proxy_status_label.setStyleSheet("color: #8C8C8C;")
            self.add_log("🌐 代理功能已禁用")
            self.add_log("⚠️ 代理配置已更改，请重启程序以应用新配置")

    def test_proxy_connection(self):
        """测试代理连接"""
        if not self.proxy_enabled_checkbox.isChecked():
            self.add_log("⚠️ 请先启用代理功能")
            return

        proxy_api_url = self.proxy_api_input.text().strip()
        if not proxy_api_url:
            self.add_log("⚠️ 请输入代理API地址")
            return

        # 检查是否已有代理测试线程在运行
        if self.proxy_test_worker and self.proxy_test_worker.isRunning():
            self.add_log("⚠️ 代理测试正在进行中，请稍候...")
            return

        # 禁用测试按钮
        self.test_proxy_button.setEnabled(False)
        self.test_proxy_button.setText("测试中...")

        # 创建代理测试工作线程
        self.proxy_test_worker = ProxyTestWorker(proxy_api_url)

        # 连接信号
        self.proxy_test_worker.log_signal.connect(self.add_log)
        self.proxy_test_worker.result_signal.connect(self.on_proxy_test_result)

        # 添加到线程管理器
        self.thread_manager.add_thread(self.proxy_test_worker)

        # 启动线程
        self.proxy_test_worker.start()

    def on_proxy_test_result(self, success, message):
        """处理代理测试结果"""
        # 恢复按钮状态
        self.test_proxy_button.setEnabled(True)
        self.test_proxy_button.setText("测试")

        if success:
            self.proxy_status_label.setText("🟢 测试成功")
            self.proxy_status_label.setStyleSheet("color: #52C41A;")
            self.add_log(f"✅ {message}")
        else:
            self.proxy_status_label.setText("🔴 测试失败")
            self.proxy_status_label.setStyleSheet("color: #FF4D4F;")
            self.add_log(f"❌ {message}")

    def get_proxy_from_api(self, api_url):
        """从API获取代理信息"""
        try:
            import requests
            response = requests.get(api_url, timeout=10)
            if response.status_code == 200:
                proxy_text = response.text.strip()
                self.add_log(f"📡 API响应: {proxy_text}")

                # 解析代理信息 (格式: ip:port)
                if ':' in proxy_text:
                    ip, port = proxy_text.split(':', 1)
                    return {
                        'ip': ip.strip(),
                        'port': port.strip(),
                        'protocol': 'socks5'
                    }
                else:
                    self.add_log("❌ 代理格式错误，期望格式: ip:port")
                    return None
            else:
                self.add_log(f"❌ API请求失败: HTTP {response.status_code}")
                return None
        except Exception as e:
            self.add_log(f"💥 API请求异常: {str(e)}")
            return None

    def test_proxy_connectivity(self, proxy_info):
        """测试代理连接性"""
        try:
            import requests

            # 构建代理配置
            proxy_url = f"socks5://{proxy_info['ip']}:{proxy_info['port']}"
            proxies = {
                'http': proxy_url,
                'https': proxy_url
            }

            # 测试连接
            test_url = "http://httpbin.org/ip"
            response = requests.get(test_url, proxies=proxies, timeout=10)

            if response.status_code == 200:
                result = response.json()
                self.add_log(f"🌐 通过代理访问成功，IP: {result.get('origin', 'Unknown')}")
                return True
            else:
                return False

        except Exception as e:
            self.add_log(f"🔍 代理连接测试失败: {str(e)}")
            return False

    def get_proxy_config(self):
        """获取代理配置"""
        if not self.proxy_enabled_checkbox.isChecked():
            return None

        proxy_api_url = self.proxy_api_input.text().strip()
        if not proxy_api_url:
            return None

        return {
            'enabled': True,
            'api_url': proxy_api_url,
            'session_locked': True  # 默认启用会话锁定
        }


class AccountRecordManager:
    """账号记录管理器"""

    def __init__(self):
        self.db_path = "account_records.db"
        self.init_database()

    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建账号记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS account_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                country_code TEXT NOT NULL,
                phone_number TEXT NOT NULL,
                access_token TEXT,
                refresh_token TEXT,
                cookies TEXT,
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'success',
                record_type TEXT DEFAULT '单独注册'
            )
        ''')

        # 检查是否需要添加record_type列（兼容旧数据库）
        cursor.execute("PRAGMA table_info(account_records)")
        columns = [column[1] for column in cursor.fetchall()]
        if 'record_type' not in columns:
            cursor.execute('ALTER TABLE account_records ADD COLUMN record_type TEXT DEFAULT "单独注册"')

        conn.commit()
        conn.close()

    def save_account_record(self, country_code, phone_number, access_token, refresh_token, cookies, record_type="单独注册"):
        """保存账号记录（如果账号已存在则覆盖）"""
        import datetime

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 使用北京时间
        beijing_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 检查是否已存在相同的账号（区号+手机号）
        cursor.execute('''
            SELECT id FROM account_records
            WHERE country_code = ? AND phone_number = ?
        ''', (country_code, phone_number))

        existing_record = cursor.fetchone()

        if existing_record:
            # 如果存在，则更新记录
            cursor.execute('''
                UPDATE account_records
                SET access_token = ?, refresh_token = ?, cookies = ?, created_time = ?, status = 'success', record_type = ?
                WHERE country_code = ? AND phone_number = ?
            ''', (access_token, refresh_token, cookies, beijing_time, record_type, country_code, phone_number))
            print(f"📝 更新账号记录: {country_code} {phone_number} -> {record_type}")
        else:
            # 如果不存在，则插入新记录
            cursor.execute('''
                INSERT INTO account_records
                (country_code, phone_number, access_token, refresh_token, cookies, created_time, status, record_type)
                VALUES (?, ?, ?, ?, ?, ?, 'success', ?)
            ''', (country_code, phone_number, access_token, refresh_token, cookies, beijing_time, record_type))
            print(f"➕ 新增账号记录: {country_code} {phone_number} -> {record_type}")

        conn.commit()
        conn.close()

    def get_all_records(self):
        """获取所有账号记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT country_code, phone_number, access_token, refresh_token, cookies, record_type, created_time
            FROM account_records
            ORDER BY created_time DESC
        ''')

        records = cursor.fetchall()
        conn.close()
        return records

    def update_record_type(self, country_code, phone_number, new_record_type):
        """更新指定账号的记录类型"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 更新记录类型
        cursor.execute('''
            UPDATE account_records
            SET record_type = ?
            WHERE country_code = ? AND phone_number = ?
        ''', (new_record_type, country_code, phone_number))

        # 检查是否更新成功
        if cursor.rowcount > 0:
            print(f"✅ 更新账号记录类型: {country_code} {phone_number} -> {new_record_type}")
        else:
            print(f"⚠️ 未找到匹配的账号记录: {country_code} {phone_number}")

        conn.commit()
        conn.close()
        return cursor.rowcount > 0

    def export_to_excel(self, file_path):
        """导出到Excel文件"""
        records = self.get_all_records()

        # 创建DataFrame
        df = pd.DataFrame(records, columns=[
            '区号', '账号', 'access_token', 'refresh_token', 'cookies', '记录类型', '创建时间'
        ])

        # 导出到Excel
        df.to_excel(file_path, index=False, engine='openpyxl')
        return len(records)


class AccountRecordWindow(QMainWindow):
    """账号记录窗口"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.record_manager = AccountRecordManager()
        self.init_ui()
        self.load_records()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("账号记录管理")
        self.setGeometry(200, 200, 1060, 800)
        self.setMinimumSize(1000, 600)

        # 设置窗口图标和样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QLabel {
                color: #333;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QPushButton#deleteButton {
                background-color: #f44336;
            }
            QPushButton#deleteButton:hover {
                background-color: #da190b;
            }
            QPushButton#exportButton {
                background-color: #2196F3;
            }
            QPushButton#exportButton:hover {
                background-color: #1976D2;
            }
            QPushButton#selectButton {
                background-color: #FF9800;
            }
            QPushButton#selectButton:hover {
                background-color: #F57C00;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                gridline-color: #e0e0e0;
                outline: none;  /* 移除焦点轮廓 */
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
                outline: none;  /* 移除单元格焦点轮廓 */
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
                outline: none;  /* 移除选中状态的焦点轮廓 */
            }
            QTableWidget::item:focus {
                outline: none;  /* 移除焦点时的虚线框 */
                border: none;   /* 移除焦点边框 */
            }
            QHeaderView::section {
                background-color: white;
                color: #333;
                padding: 8px;
                border: 1px solid #e0e0e0;
                border-bottom: 1px solid #e0e0e0;
                font-weight: bold;
                text-align: center;
            }
            QCheckBox {
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #ccc;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #4CAF50;
                background-color: #4CAF50;
                border-radius: 3px;
            }
            QComboBox#searchCombo {
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                padding: 5px 10px;
                background-color: white;
                font-size: 12px;
                min-height: 20px;
            }
            QComboBox#searchCombo:hover {
                border-color: #40a9ff;
            }
            QComboBox#searchCombo:focus {
                border-color: #1890ff;
                outline: none;
            }
            QComboBox#searchCombo::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox#searchCombo::down-arrow {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMgNUw2IDhMOSA1IiBzdHJva2U9IiM2NjYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                width: 12px;
                height: 12px;
            }
        """)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(25, 25, 25, 25)

        # 标题区域
        title_layout = QHBoxLayout()
        title_label = QLabel("📋 账号记录管理")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        main_layout.addLayout(title_layout)

        # 操作按钮区域
        button_layout = QHBoxLayout()

        # 搜索区域
        search_label = QLabel("🔍 记录类型:")
        search_label.setStyleSheet("font-weight: bold; color: #666;")
        button_layout.addWidget(search_label)

        self.search_combo = QComboBox()
        self.search_combo.addItems(["全部记录", "单独注册", "注册成功并答题", "高危"])
        self.search_combo.setMinimumWidth(120)
        self.search_combo.setObjectName("searchCombo")
        self.search_combo.currentTextChanged.connect(self.filter_records)
        button_layout.addWidget(self.search_combo)

        button_layout.addSpacing(15)

        # 智能全选按钮
        self.toggle_select_button = QPushButton("✅ 全选")
        self.toggle_select_button.setObjectName("selectButton")
        self.toggle_select_button.clicked.connect(self.toggle_select_all)
        button_layout.addWidget(self.toggle_select_button)

        button_layout.addSpacing(15)

        # 刷新按钮
        refresh_button = QPushButton("🔄 刷新")
        refresh_button.clicked.connect(self.load_records)
        button_layout.addWidget(refresh_button)

        button_layout.addSpacing(10)

        # 导出按钮组
        export_selected_button = QPushButton("📤 导出选中")
        export_selected_button.setObjectName("exportButton")
        export_selected_button.clicked.connect(self.export_selected_records)
        button_layout.addWidget(export_selected_button)

        export_all_button = QPushButton("📋 导出全部")
        export_all_button.setObjectName("exportButton")
        export_all_button.clicked.connect(self.export_all_records)
        button_layout.addWidget(export_all_button)

        button_layout.addSpacing(10)

        # 删除按钮
        delete_selected_button = QPushButton("🗑️ 删除选中")
        delete_selected_button.setObjectName("deleteButton")
        delete_selected_button.clicked.connect(self.delete_selected_records)
        button_layout.addWidget(delete_selected_button)

        button_layout.addStretch()  # 添加弹性空间
        main_layout.addLayout(button_layout)

        # 表格
        self.table = QTableWidget()
        self.table.setColumnCount(9)  # 增加序号列、复选框列和记录类型列
        self.table.setHorizontalHeaderLabels([
            "序号", "选择", "区号", "账号", "access_token", "refresh_token", "cookies", "记录类型", "创建时间"
        ])

        # 设置表格属性
        self.table.setAlternatingRowColors(False)
        self.table.setSelectionBehavior(QTableWidget.SelectItems)  # 改为选择单个单元格
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.table.setShowGrid(True)
        self.table.verticalHeader().setVisible(False)
        self.table.setFocusPolicy(Qt.StrongFocus)  # 允许接收键盘事件以支持复制功能

        # 连接键盘事件
        self.table.keyPressEvent = self.table_key_press_event

        # 设置列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # 选择列
        self.table.setColumnWidth(0, 60)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 选择
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 区号
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 账号
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # access_token
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # refresh_token
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # cookies
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # 记录类型
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # 创建时间

        main_layout.addWidget(self.table)

        # 状态栏
        self.statusBar().showMessage("就绪")
        self.statusBar().setStyleSheet("QStatusBar { color: #666; }")

    def get_records_statistics(self, records):
        """获取记录统计信息"""
        total_count = len(records)

        # 统计各种记录类型
        type_counts = {}
        for record in records:
            record_type = record[5] if len(record) > 5 else "未知类型"
            type_counts[record_type] = type_counts.get(record_type, 0) + 1

        # 构建统计字符串
        stats_parts = [f"总计{total_count}条记录"]

        # 按优先级顺序显示记录类型
        priority_types = ["单独注册", "注册成功并答题", "单独登录", "登录成功并答题", "仅答题成功", "高危"]

        # 添加老用户1-6级类型
        old_user_types = [f"老用户{level}级" for level in range(1, 7)]
        priority_types.extend(old_user_types)

        for record_type in priority_types:
            count = type_counts.get(record_type, 0)
            if count > 0:  # 只显示有记录的类型
                if record_type == "单独注册":
                    stats_parts.append(f"单独注册: {count}")
                elif record_type == "注册成功并答题":
                    stats_parts.append(f"注册并答题: {count}")
                elif record_type == "单独登录":
                    stats_parts.append(f"单独登录: {count}")
                elif record_type == "登录成功并答题":
                    stats_parts.append(f"登录并答题: {count}")
                elif record_type == "仅答题成功":
                    stats_parts.append(f"仅答题: {count}")
                elif record_type == "高危":
                    stats_parts.append(f"高危: {count}")
                elif record_type.startswith("老用户") and record_type.endswith("级"):
                    # 老用户记录，提取等级数字
                    level_str = record_type[3:-1]
                    stats_parts.append(f"老用户{level_str}级: {count}")

        # 处理其他未知类型
        for record_type, count in type_counts.items():
            if record_type not in priority_types and count > 0:
                stats_parts.append(f"{record_type}: {count}")

        return " | ".join(stats_parts)

    def load_records(self):
        """加载记录"""
        try:
            records = self.record_manager.get_all_records()

            # 重置筛选下拉框到"全部记录"
            self.search_combo.setCurrentText("全部记录")

            self.table.setRowCount(len(records))

            for row, record in enumerate(records):
                # 第0列添加序号
                seq_item = QTableWidgetItem(str(row + 1))
                seq_item.setTextAlignment(Qt.AlignCenter)
                self.table.setItem(row, 0, seq_item)

                # 第1列添加复选框
                checkbox = QCheckBox()
                checkbox.setStyleSheet("QCheckBox { margin: 5px; }")
                checkbox.stateChanged.connect(self.update_select_all_state)
                self.table.setCellWidget(row, 1, checkbox)

                # 其他列显示数据（索引需要+2）
                for col, value in enumerate(record):
                    # 对于长文本进行截断显示
                    if col in [2, 3, 4] and value and len(str(value)) > 50:
                        display_value = str(value)[:47] + "..."
                    else:
                        display_value = str(value) if value else ""

                    item = QTableWidgetItem(display_value)
                    # 设置完整内容为工具提示
                    if value:
                        item.setToolTip(str(value))
                    self.table.setItem(row, col + 2, item)  # +2 因为第0列是序号，第1列是复选框

            # 调整列宽
            self.adjust_column_widths()

            # 更新状态栏信息
            stats_message = self.get_records_statistics(records)
            self.statusBar().showMessage(stats_message)

            # 更新按钮状态
            self.update_toggle_button_text()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载记录失败：{str(e)}")

    def toggle_select_all(self):
        """智能切换全选/取消全选"""
        # 检查当前选择状态
        total_rows = self.table.rowCount()
        if total_rows == 0:
            return

        checked_count = 0
        for row in range(total_rows):
            checkbox = self.table.cellWidget(row, 1)
            if checkbox and checkbox.isChecked():
                checked_count += 1

        # 如果全部选中，则取消全选；否则全选
        if checked_count == total_rows:
            self.unselect_all()
        else:
            self.select_all()

    def select_all(self):
        """全选所有记录"""
        for row in range(self.table.rowCount()):
            checkbox = self.table.cellWidget(row, 1)
            if checkbox:
                checkbox.blockSignals(True)  # 暂时阻塞信号避免频繁更新
                checkbox.setChecked(True)
                checkbox.blockSignals(False)
        self.update_toggle_button_text()

    def unselect_all(self):
        """取消全选"""
        for row in range(self.table.rowCount()):
            checkbox = self.table.cellWidget(row, 1)
            if checkbox:
                checkbox.blockSignals(True)  # 暂时阻塞信号避免频繁更新
                checkbox.setChecked(False)
                checkbox.blockSignals(False)
        self.update_toggle_button_text()

    def update_select_all_state(self):
        """更新选择状态和按钮文本"""
        self.update_toggle_button_text()

    def update_toggle_button_text(self):
        """更新切换按钮的文本"""
        total_rows = self.table.rowCount()
        if total_rows == 0:
            self.toggle_select_button.setText("✅ 全选")
            return

        checked_count = 0
        for row in range(total_rows):
            checkbox = self.table.cellWidget(row, 1)
            if checkbox and checkbox.isChecked():
                checked_count += 1

        if checked_count == 0:
            self.toggle_select_button.setText("✅ 全选")
        elif checked_count == total_rows:
            self.toggle_select_button.setText("❌ 取消全选")
        else:
            self.toggle_select_button.setText(f"✅ 全选 ({checked_count}/{total_rows})")

    def filter_records(self, filter_type):
        """根据类型筛选记录"""
        try:
            # 获取所有记录
            all_records = self.record_manager.get_all_records()

            # 根据筛选类型过滤记录
            filtered_records = []

            if filter_type == "全部记录":
                filtered_records = all_records
            elif filter_type == "单独注册":
                # 筛选记录类型为"单独注册"的记录
                filtered_records = [r for r in all_records if len(r) > 5 and r[5] == "单独注册"]
            elif filter_type == "注册成功并答题":
                # 筛选记录类型为"注册成功并答题"的记录
                filtered_records = [r for r in all_records if len(r) > 5 and r[5] == "注册成功并答题"]
            elif filter_type == "高危":
                # 筛选记录类型为"高危"的记录
                filtered_records = [r for r in all_records if len(r) > 5 and r[5] == "高危"]

            # 更新表格显示
            self.display_filtered_records(filtered_records)

            # 更新状态栏信息
            stats_message = self.get_records_statistics(filtered_records)
            self.statusBar().showMessage(f"筛选结果: {stats_message}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"筛选记录失败：{str(e)}")

    def display_filtered_records(self, records):
        """显示筛选后的记录"""
        self.table.setRowCount(len(records))

        for row, record in enumerate(records):
            # 第0列添加序号
            seq_item = QTableWidgetItem(str(row + 1))
            seq_item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 0, seq_item)

            # 第1列添加复选框
            checkbox = QCheckBox()
            checkbox.setStyleSheet("QCheckBox { margin: 5px; }")
            checkbox.stateChanged.connect(self.update_select_all_state)
            self.table.setCellWidget(row, 1, checkbox)

            # 其他列显示数据（索引需要+2）
            for col, value in enumerate(record):
                # 对于长文本进行截断显示
                if col in [2, 3, 4] and value and len(str(value)) > 50:
                    display_value = str(value)[:47] + "..."
                else:
                    display_value = str(value) if value else ""

                item = QTableWidgetItem(display_value)
                # 设置完整内容为工具提示
                if value:
                    item.setToolTip(str(value))
                self.table.setItem(row, col + 2, item)  # +2 因为第0列是序号，第1列是复选框

        # 调整列宽
        self.adjust_column_widths()

        # 更新按钮状态
        self.update_toggle_button_text()

    def adjust_column_widths(self):
        """调整表格列宽"""
        header = self.table.horizontalHeader()

        # 序号列固定宽度
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.resizeSection(0, 50)

        # 复选框列固定宽度
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.resizeSection(1, 60)

        # 区号列固定宽度
        header.setSectionResizeMode(2, QHeaderView.Fixed)
        header.resizeSection(2, 60)

        # 账号列固定宽度
        header.setSectionResizeMode(3, QHeaderView.Fixed)
        header.resizeSection(3, 100)

        # access_token列 - 可调整，缩小默认宽度
        header.setSectionResizeMode(4, QHeaderView.Interactive)
        header.resizeSection(4, 150)

        # refresh_token列 - 可调整，缩小默认宽度
        header.setSectionResizeMode(5, QHeaderView.Interactive)
        header.resizeSection(5, 150)

        # cookies列 - 可调整，缩小默认宽度
        header.setSectionResizeMode(6, QHeaderView.Interactive)
        header.resizeSection(6, 150)

        # 记录类型列 - 固定宽度
        header.setSectionResizeMode(7, QHeaderView.Fixed)
        header.resizeSection(7, 140)

        # 创建时间列 - 拉伸填充剩余空间
        header.setSectionResizeMode(8, QHeaderView.Stretch)

    def get_selected_rows(self):
        """获取选中的行"""
        selected_rows = []
        for row in range(self.table.rowCount()):
            checkbox = self.table.cellWidget(row, 1)
            if checkbox and checkbox.isChecked():
                selected_rows.append(row)
        return selected_rows

    def export_selected_records(self):
        """导出选中的记录"""
        try:
            selected_rows = self.get_selected_rows()
            if not selected_rows:
                QMessageBox.warning(self, "提示", "请先选择要导出的记录！")
                return

            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出选中记录",
                f"选中记录_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel文件 (*.xlsx)"
            )

            if file_path:
                # 获取选中行的数据
                all_records = self.record_manager.get_all_records()
                selected_records = [all_records[row] for row in selected_rows]

                # 导出选中的记录
                count = self.export_records_to_file(selected_records, file_path)
                QMessageBox.information(self, "成功", f"已导出 {count} 条选中记录到：\n{file_path}")
                self.statusBar().showMessage(f"已导出 {count} 条选中记录")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出选中记录失败：{str(e)}")

    def export_all_records(self):
        """导出全部记录"""
        try:
            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出全部记录",
                f"全部记录_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel文件 (*.xlsx)"
            )

            if file_path:
                count = self.record_manager.export_to_excel(file_path)
                QMessageBox.information(self, "成功", f"已导出 {count} 条记录到：\n{file_path}")
                self.statusBar().showMessage(f"已导出 {count} 条记录")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出全部记录失败：{str(e)}")

    def export_records_to_file(self, records, file_path):
        """将记录导出到文件"""
        import pandas as pd

        # 转换为DataFrame
        df = pd.DataFrame(records, columns=[
            '区号', '账号', 'access_token', 'refresh_token', 'cookies', '记录类型', '创建时间'
        ])

        # 导出到Excel
        df.to_excel(file_path, index=False, engine='openpyxl')
        return len(records)

    def delete_selected_records(self):
        """删除选中的记录"""
        try:
            selected_rows = self.get_selected_rows()
            if not selected_rows:
                QMessageBox.warning(self, "提示", "请先选择要删除的记录！")
                return

            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除选中的 {len(selected_rows)} 条记录吗？此操作不可恢复！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 获取要删除的记录信息
                all_records = self.record_manager.get_all_records()
                records_to_delete = []

                for row in selected_rows:
                    if row < len(all_records):
                        record = all_records[row]
                        # 使用区号和账号作为唯一标识
                        records_to_delete.append((record[0], record[1]))  # (区号, 账号)

                # 从数据库删除
                conn = sqlite3.connect(self.record_manager.db_path)
                cursor = conn.cursor()

                deleted_count = 0
                for country_code, phone_number in records_to_delete:
                    cursor.execute(
                        "DELETE FROM account_records WHERE country_code = ? AND phone_number = ?",
                        (country_code, phone_number)
                    )
                    deleted_count += cursor.rowcount

                conn.commit()
                conn.close()

                # 重新加载记录
                self.load_records()
                QMessageBox.information(self, "成功", f"已删除 {deleted_count} 条记录")
                self.statusBar().showMessage(f"已删除 {deleted_count} 条记录")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除记录失败：{str(e)}")

    def table_key_press_event(self, event):
        """处理表格的键盘事件"""
        # 检查是否是Ctrl+C组合键
        if event.key() == Qt.Key_C and event.modifiers() == Qt.ControlModifier:
            self.copy_selected_content()
        else:
            # 调用原始的键盘事件处理
            QTableWidget.keyPressEvent(self.table, event)

    def copy_selected_content(self):
        """复制选中的表格内容到剪贴板"""
        try:
            # 获取选中的单元格
            selected_items = self.table.selectedItems()

            if not selected_items:
                # 如果没有选中单元格，复制整个表格内容
                self.copy_all_table_content()
                return

            # 获取选中区域的行列范围
            selected_rows = set()
            selected_cols = set()

            for item in selected_items:
                selected_rows.add(item.row())
                selected_cols.add(item.column())

            # 排序行列
            sorted_rows = sorted(selected_rows)
            sorted_cols = sorted(selected_cols)

            # 构建复制内容
            copy_data = []

            # 添加表头（只包含选中的列）
            headers = []
            for col in sorted_cols:
                header_item = self.table.horizontalHeaderItem(col)
                if header_item:
                    headers.append(header_item.text())
                else:
                    headers.append(f"列{col}")
            copy_data.append('\t'.join(headers))

            # 添加数据行
            for row in sorted_rows:
                row_data = []
                for col in sorted_cols:
                    # 处理复选框列（第1列）
                    if col == 1:
                        checkbox = self.table.cellWidget(row, col)
                        if checkbox and checkbox.isChecked():
                            row_data.append("✓")
                        else:
                            row_data.append("")
                    else:
                        # 处理普通单元格
                        item = self.table.item(row, col)
                        if item:
                            # 获取完整内容（从工具提示或显示文本）
                            full_text = item.toolTip() if item.toolTip() else item.text()
                            row_data.append(full_text)
                        else:
                            row_data.append("")
                copy_data.append('\t'.join(row_data))

            # 复制到剪贴板
            clipboard_text = '\n'.join(copy_data)
            clipboard = QApplication.clipboard()
            clipboard.setText(clipboard_text)

            # 显示提示信息
            self.statusBar().showMessage(f"已复制 {len(sorted_rows)} 行 {len(sorted_cols)} 列的内容到剪贴板")

        except Exception as e:
            QMessageBox.warning(self, "复制失败", f"复制内容时出错：{str(e)}")

    def copy_all_table_content(self):
        """复制整个表格内容到剪贴板"""
        try:
            copy_data = []

            # 添加表头
            headers = []
            for col in range(self.table.columnCount()):
                header_item = self.table.horizontalHeaderItem(col)
                if header_item:
                    headers.append(header_item.text())
                else:
                    headers.append(f"列{col}")
            copy_data.append('\t'.join(headers))

            # 添加所有数据行
            for row in range(self.table.rowCount()):
                row_data = []
                for col in range(self.table.columnCount()):
                    # 处理复选框列（第1列）
                    if col == 1:
                        checkbox = self.table.cellWidget(row, col)
                        if checkbox and checkbox.isChecked():
                            row_data.append("✓")
                        else:
                            row_data.append("")
                    else:
                        # 处理普通单元格
                        item = self.table.item(row, col)
                        if item:
                            # 获取完整内容（从工具提示或显示文本）
                            full_text = item.toolTip() if item.toolTip() else item.text()
                            row_data.append(full_text)
                        else:
                            row_data.append("")
                copy_data.append('\t'.join(row_data))

            # 复制到剪贴板
            clipboard_text = '\n'.join(copy_data)
            clipboard = QApplication.clipboard()
            clipboard.setText(clipboard_text)

            # 显示提示信息
            self.statusBar().showMessage(f"已复制整个表格内容（{self.table.rowCount()} 行）到剪贴板")

        except Exception as e:
            QMessageBox.warning(self, "复制失败", f"复制表格内容时出错：{str(e)}")


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 不设置应用信息

    # 创建主窗口
    window = BilibiliSMSGUI()
    window.show()

    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
